/** @type {import('tailwindcss').Config} */
export default {
  content: ["./src/**/*.{html,ts,tsx}"],
  theme: {
    extend: {
      colors: {
        // Traiteria Brand Colors
        traiteria: {
          primary: '#672B32',      // Couleur principale - rouge bordeaux
          secondary: '#FCFAF4',    // Couleur secondaire - crème/beige clair
          'primary-light': '#8B3A42',   // Version plus claire de la couleur principale
          'primary-dark': '#4A1F25',    // Version plus foncée de la couleur principale
          'secondary-dark': '#F5F1E8',  // Version plus foncée de la couleur secondaire
        },
        // Alias pour faciliter l'utilisation
        primary: '#672B32',
        secondary: '#FCFAF4',
      },
      fontFamily: {
        // Vous pouvez ajouter des polices personnalisées ici si nécessaire
        'brand': ['Avenir', 'Helvetica Neue', 'Arial', 'Helvetica', 'sans-serif'],
      },
    },
  },
  plugins: [],
}
