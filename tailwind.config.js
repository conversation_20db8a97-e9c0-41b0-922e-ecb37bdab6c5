/** @type {import('tailwindcss').Config} */
export default {
  content: ["./src/**/*.{html,ts,tsx}"],
  theme: {
    extend: {
      colors: {
        // Traiteria Brand Colors
        traiteria: {
          primary: '#9F1512',      // Couleur principale - rouge
          secondary: '#ffffff',    // Couleur secondaire - blanc
          'primary-light': '#B91E1A',   // Version plus claire de la couleur principale
          'primary-dark': '#7A100E',    // Version plus foncée de la couleur principale
          'secondary-dark': '#f5f5f5',  // Version plus foncée de la couleur secondaire
        },
        // Alias pour faciliter l'utilisation
        primary: '#9F1512',
        secondary: '#ffffff',
      },
      fontFamily: {
        // Vous pouvez ajouter des polices personnalisées ici si nécessaire
        'brand': ['Avenir', 'Helvetica Neue', 'Arial', 'Helvetica', 'sans-serif'],
      },
    },
  },
  plugins: [],
}
