/** @type {import('tailwindcss').Config} */
export default {
  content: ["./src/**/*.{html,ts,tsx}"],
  theme: {
    extend: {
      colors: {
        // Traiteria Brand Colors
        traiteria: {
          primary: '#8A3A45',      // Couleur principale - rouge bordeaux
          secondary: '#FCFAF4',    // Couleur de fond de l'application - crème très clair
          sidebar: '#ffffff',      // Couleur de fond de la sidebar - blanc
          'primary-light': '#A04A56',   // Version plus claire de la couleur principale
          'primary-dark': '#742F38',    // Version plus foncée de la couleur principale
          'secondary-dark': '#f5f5f5',  // Version plus foncée du blanc
        },
        // Alias pour faciliter l'utilisation
        primary: '#8A3A45',
        secondary: '#FCFAF4',
      },
      fontFamily: {
        // Vous pouvez ajouter des polices personnalisées ici si nécessaire
        'brand': ['Avenir', 'Helvetica Neue', 'Arial', 'Helvetica', 'sans-serif'],
      },
    },
  },
  plugins: [],
}
