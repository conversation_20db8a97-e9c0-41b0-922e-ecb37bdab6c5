/** @type {import('tailwindcss').Config} */
export default {
  content: ["./src/**/*.{html,ts,tsx}"],
  theme: {
    extend: {
      colors: {
        // Traiteria Brand Colors
        traiteria: {
          primary: '#9F1512',      // Couleur principale - rouge
          secondary: '#FCFAF4',    // Couleur de fond de l'application
          sidebar: '#EBE6DD',      // Couleur de fond de la sidebar
          'primary-light': '#B91E1A',   // Version plus claire de la couleur principale
          'primary-dark': '#7A100E',    // Version plus foncée de la couleur principale
          'secondary-dark': '#F5F1E8',  // Version plus foncée du fond
        },
        // Alias pour faciliter l'utilisation
        primary: '#9F1512',
        secondary: '#FCFAF4',
      },
      fontFamily: {
        // Vous pouvez ajouter des polices personnalisées ici si nécessaire
        'brand': ['Avenir', 'Helvetica Neue', 'Arial', 'Helvetica', 'sans-serif'],
      },
    },
  },
  plugins: [],
}
