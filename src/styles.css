@tailwind base;
@tailwind components;
@tailwind utilities;

/* Traiteria Global Styles */
:root {
  --traiteria-primary: #9F1512;
  --traiteria-secondary: #FCFAF4;
  --traiteria-sidebar: #EBE6DD;
  --traiteria-primary-light: #B91E1A;
  --traiteria-primary-dark: #7A100E;
  --traiteria-secondary-dark: #F5F1E8;
}

/* Global body styling with Traiteria colors */
body {
  font-family: 'Avenir', 'Helvetica Neue', Arial, Helvetica, sans-serif;
  background-color: var(--traiteria-secondary);
}

/* Styles pour ng-zorro menu avec couleurs Traiteria */
.ant-menu {
  background-color: var(--traiteria-sidebar) !important;
}

.ant-menu-item {
  color: #000000d9 !important;
  background-color: transparent !important;
}

.ant-menu-item-selected {
  background-color: var(--traiteria-primary) !important;
  color: #fff !important;
}

.ant-menu-item:hover {
  background-color: var(--traiteria-primary) !important;
  color: #fff !important;
}

.ant-menu-submenu-title {
  color: #000000d9 !important;
  background-color: transparent !important;
}

.ant-menu-submenu-title:hover {
  color: var(--traiteria-primary) !important;
  background-color: transparent !important;
}

.ant-menu-submenu-title.ant-menu-submenu-selected {
  color: var(--traiteria-primary) !important;
}

/* Styles spécifiques pour les sous-menus */
.ant-menu-sub {
  background-color: var(--traiteria-sidebar) !important;
}

.ant-menu-sub .ant-menu-item {
  color: #000000d9 !important;
  background-color: transparent !important;
  padding-left: 48px !important;
}

.ant-menu-sub .ant-menu-item-selected {
  background-color: var(--traiteria-primary) !important;
  color: #fff !important;
}

.ant-menu-sub .ant-menu-item:hover {
  background-color: var(--traiteria-primary) !important;
  color: #fff !important;
}

/* Correction pour les icônes */
.ant-menu-item .anticon,
.ant-menu-submenu-title .anticon {
  color: inherit !important;
}

/* Utility classes for Traiteria colors */
.text-traiteria-primary {
  color: var(--traiteria-primary) !important;
}

.bg-traiteria-primary {
  background-color: var(--traiteria-primary) !important;
}

.text-traiteria-secondary {
  color: var(--traiteria-secondary) !important;
}

.bg-traiteria-secondary {
  background-color: var(--traiteria-secondary) !important;
}

.border-traiteria-primary {
  border-color: var(--traiteria-primary) !important;
}

