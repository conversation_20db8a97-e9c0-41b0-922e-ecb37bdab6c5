@tailwind base;
@tailwind components;
@tailwind utilities;

/* Traiteria Global Styles */
:root {
  --traiteria-primary: #9F1512;
  --traiteria-secondary: #ffffff;
  --traiteria-primary-light: #B91E1A;
  --traiteria-primary-dark: #7A100E;
  --traiteria-secondary-dark: #f5f5f5;
}

/* Global body styling with Traiteria colors */
body {
  font-family: 'Avenir', 'Helvetica Neue', Arial, Helvetica, sans-serif;
  background-color: var(--traiteria-secondary);
}

/* Utility classes for Traiteria colors */
.text-traiteria-primary {
  color: var(--traiteria-primary) !important;
}

.bg-traiteria-primary {
  background-color: var(--traiteria-primary) !important;
}

.text-traiteria-secondary {
  color: var(--traiteria-secondary) !important;
}

.bg-traiteria-secondary {
  background-color: var(--traiteria-secondary) !important;
}

.border-traiteria-primary {
  border-color: var(--traiteria-primary) !important;
}

