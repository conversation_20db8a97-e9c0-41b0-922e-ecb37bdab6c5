@tailwind base;
@tailwind components;
@tailwind utilities;

/* Traiteria Global Styles */
:root {
  --traiteria-primary: #B1955B;
  --traiteria-secondary: #EBE6DD;
  --traiteria-primary-light: #C4A76F;
  --traiteria-primary-dark: #9E8347;
  --traiteria-secondary-dark: #DDD4C9;
}

/* Global body styling with Traiteria colors */
body {
  font-family: 'Avenir', 'Helvetica Neue', Arial, Helvetica, sans-serif;
  background-color: var(--traiteria-secondary);
}

/* Utility classes for Traiteria colors */
.text-traiteria-primary {
  color: var(--traiteria-primary) !important;
}

.bg-traiteria-primary {
  background-color: var(--traiteria-primary) !important;
}

.text-traiteria-secondary {
  color: var(--traiteria-secondary) !important;
}

.bg-traiteria-secondary {
  background-color: var(--traiteria-secondary) !important;
}

.border-traiteria-primary {
  border-color: var(--traiteria-primary) !important;
}

