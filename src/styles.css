@tailwind base;
@tailwind components;
@tailwind utilities;

/* Traiteria Global Styles */
:root {
  --traiteria-primary: #672B32;
  --traiteria-secondary: #FCFAF4;
  --traiteria-primary-light: #8B3A42;
  --traiteria-primary-dark: #4A1F25;
  --traiteria-secondary-dark: #F5F1E8;
}

/* Global body styling with Traiteria colors */
body {
  font-family: 'Avenir', 'Helvetica Neue', Arial, Helvetica, sans-serif;
  background-color: var(--traiteria-secondary);
}

/* Utility classes for Traiteria colors */
.text-traiteria-primary {
  color: var(--traiteria-primary) !important;
}

.bg-traiteria-primary {
  background-color: var(--traiteria-primary) !important;
}

.text-traiteria-secondary {
  color: var(--traiteria-secondary) !important;
}

.bg-traiteria-secondary {
  background-color: var(--traiteria-secondary) !important;
}

.border-traiteria-primary {
  border-color: var(--traiteria-primary) !important;
}

