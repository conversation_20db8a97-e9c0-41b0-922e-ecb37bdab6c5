@tailwind base;
@tailwind components;
@tailwind utilities;

/* Apple-inspired Global Styles */
:root {
  --apple-black: #000000;
  --apple-white: #ffffff;
  --apple-gray-50: #fafafa;
  --apple-gray-100: #f5f5f7;
  --apple-gray-200: #e5e5e7;
  --apple-gray-300: #d2d2d7;
  --apple-gray-600: #6e6e73;
  --apple-gray-900: #1d1d1f;
  --apple-grenat: #8B1538;
  --apple-grenat-light: #A91B47;
  --apple-grenat-dark: #6B0F2A;
  --apple-green: #34c759;
  --apple-orange: #ff9500;
  --apple-red: #ff3b30;
  --apple-blue: #007aff;
  --apple-purple: #af52de;
  --apple-pink: #ff2d92;
  --apple-teal: #5ac8fa;
  --apple-indigo: #5856d6;

  /* Gradients Grenat Apple sympas */
  --gradient-primary: linear-gradient(135deg, var(--apple-grenat) 0%, var(--apple-grenat-light) 100%);
  --gradient-secondary: linear-gradient(135deg, var(--apple-gray-100) 0%, var(--apple-white) 100%);
  --gradient-card: linear-gradient(135deg, var(--apple-white) 0%, var(--apple-gray-50) 100%);
  --gradient-button: linear-gradient(135deg, var(--apple-grenat) 0%, var(--apple-grenat-dark) 100%);
  --gradient-hover: linear-gradient(135deg, var(--apple-grenat-light) 0%, var(--apple-grenat) 100%);

  /* Dégradés colorés sympas */
  --gradient-grenat: linear-gradient(135deg, var(--apple-grenat) 0%, var(--apple-grenat-dark) 100%);
  --gradient-blue: linear-gradient(135deg, var(--apple-blue) 0%, #0056d6 100%);
  --gradient-green: linear-gradient(135deg, var(--apple-green) 0%, #28a745 100%);
  --gradient-purple: linear-gradient(135deg, var(--apple-purple) 0%, #8e44ad 100%);
  --gradient-pink: linear-gradient(135deg, var(--apple-pink) 0%, #e91e63 100%);
  --gradient-teal: linear-gradient(135deg, var(--apple-teal) 0%, #17a2b8 100%);
  --gradient-orange: linear-gradient(135deg, var(--apple-orange) 0%, #fd7e14 100%);

  /* Dégradés subtils pour backgrounds */
  --gradient-glass: linear-gradient(135deg, rgba(255,255,255,0.9) 0%, rgba(255,255,255,0.7) 100%);
  --gradient-frosted: linear-gradient(135deg, rgba(248,249,250,0.95) 0%, rgba(233,236,239,0.9) 100%);

  /* Shadows */
  --shadow-sm: 0 2px 8px rgba(0, 0, 0, 0.06);
  --shadow-md: 0 4px 16px rgba(0, 0, 0, 0.08);
  --shadow-lg: 0 8px 24px rgba(0, 0, 0, 0.12);
  --shadow-xl: 0 16px 32px rgba(0, 0, 0, 0.16);

  /* Border radius */
  --radius-sm: 8px;
  --radius-md: 12px;
  --radius-lg: 16px;
  --radius-xl: 20px;
}

/* Global body styling with Traiteria colors */
body {
  font-family: 'Avenir', 'Helvetica Neue', Arial, Helvetica, sans-serif;
  background-color: var(--traiteria-secondary);
}

/* Amélioration de la distinction sidebar/contenu */
.ant-layout-sider {
  border-right: 1px solid #f0f0f0 !important;
}

/* Styles pour ng-zorro menu avec couleurs Traiteria */
.ant-menu {
  background-color: var(--traiteria-sidebar) !important;
  border-right: 1px solid #f0f0f0 !important;
}

.ant-menu-item {
  color: #000000d9 !important;
  background-color: transparent !important;
}

.ant-menu-item-selected {
  background-color: var(--traiteria-primary) !important;
  color: #ffffff !important;
}

.ant-menu-item-selected .anticon {
  color: #ffffff !important;
}

.ant-menu-item-selected a {
  color: #ffffff !important;
}

.ant-menu-item-selected span {
  color: #ffffff !important;
}

/* Sidebar ng-zorro normal - juste les couleurs grenat */

/* Apple-style Form Controls */
.ant-select,
.ant-input,
.ant-input-number,
.ant-picker,
.ant-input-group {
  width: 100% !important;
  border-radius: var(--radius-md) !important;
  border: 1px solid var(--apple-gray-200) !important;
  transition: all 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94) !important;
}

.ant-input:hover,
.ant-select-selector:hover,
.ant-picker:hover {
  border-color: var(--apple-gray-300) !important;
  box-shadow: var(--shadow-sm) !important;
}

.ant-input:focus,
.ant-select-focused .ant-select-selector,
.ant-picker-focused {
  border-color: var(--apple-grenat) !important;
  box-shadow: 0 0 0 2px rgba(139, 21, 56, 0.2) !important;
}

.ant-form-item-control-input {
  width: 100% !important;
}

.ant-form-item-control-input-content {
  width: 100% !important;
}

/* Apple-style Buttons */
.ant-btn-primary {
  background: var(--gradient-grenat) !important;
  border: none !important;
  border-radius: var(--radius-md) !important;
  font-weight: 500 !important;
  box-shadow: var(--shadow-sm) !important;
  transition: all 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94) !important;
  color: var(--apple-white) !important;
}

.ant-btn-primary:hover {
  background: var(--gradient-hover) !important;
  transform: translateY(-2px) !important;
  box-shadow: 0 8px 25px rgba(139, 21, 56, 0.3) !important;
}

.ant-btn-default {
  background: var(--apple-white) !important;
  border: 1px solid var(--apple-gray-200) !important;
  border-radius: var(--radius-md) !important;
  color: var(--apple-gray-900) !important;
  font-weight: 500 !important;
  transition: all 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94) !important;
}

.ant-btn-default:hover {
  background: var(--apple-gray-50) !important;
  border-color: var(--apple-gray-300) !important;
  transform: translateY(-1px) !important;
  box-shadow: var(--shadow-sm) !important;
}

/* Apple-style Cards */
.ant-card {
  border-radius: var(--radius-lg) !important;
  border: 1px solid var(--apple-gray-200) !important;
  box-shadow: var(--shadow-sm) !important;
  background: var(--apple-white) !important;
}

.ant-card-head {
  border-bottom: 1px solid var(--apple-gray-200) !important;
  background: var(--gradient-secondary) !important;
}

.ant-card-head-title {
  color: var(--apple-gray-900) !important;
  font-weight: 600 !important;
}

/* Apple-style Tables */
.ant-table {
  border-radius: var(--radius-lg) !important;
  overflow: hidden !important;
}

.ant-table-thead > tr > th {
  background: #f5f5f7 !important; /* Gris Apple doux */
  color: #1d1d1f !important;
  border-bottom: 1px solid #e5e5e7 !important;
  font-weight: 600 !important;
}

.ant-table-tbody > tr:hover > td {
  background: var(--apple-gray-50) !important;
}

/* Apple-style Modals */
.ant-modal-content {
  border-radius: var(--radius-xl) !important;
  box-shadow: var(--shadow-xl) !important;
  border: 1px solid var(--apple-gray-200) !important;
}

.ant-modal-header {
  border-bottom: 1px solid var(--apple-gray-200) !important;
  background: var(--gradient-secondary) !important;
  border-radius: var(--radius-xl) var(--radius-xl) 0 0 !important;
}

/* Apple-style Notifications */
.ant-notification {
  border-radius: var(--radius-lg) !important;
  box-shadow: var(--shadow-lg) !important;
}

/* Apple-style Steps */
.ant-steps-item-process .ant-steps-item-icon {
  background: var(--gradient-button) !important;
  border-color: var(--apple-black) !important;
}

.ant-steps-item-finish .ant-steps-item-icon {
  background: var(--apple-green) !important;
  border-color: var(--apple-green) !important;
}

/* Global Typography - Apple style */
body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif !important;
  color: var(--apple-gray-900) !important;
  background: var(--apple-gray-50) !important;
}

h1, h2, h3, h4, h5, h6 {
  color: var(--apple-black) !important;
  font-weight: 600 !important;
}

/* Smooth scrolling */
* {
  scroll-behavior: smooth;
}

/* Selection styling */
::selection {
  background: var(--apple-grenat);
  color: var(--apple-white);
}

/* Classes utilitaires pour dégradés sympas */
.gradient-grenat {
  background: var(--gradient-grenat) !important;
  color: var(--apple-white) !important;
}

.gradient-blue {
  background: var(--gradient-blue) !important;
  color: var(--apple-white) !important;
}

.gradient-green {
  background: var(--gradient-green) !important;
  color: var(--apple-white) !important;
}

.gradient-purple {
  background: var(--gradient-purple) !important;
  color: var(--apple-white) !important;
}

.gradient-pink {
  background: var(--gradient-pink) !important;
  color: var(--apple-white) !important;
}

.gradient-teal {
  background: var(--gradient-teal) !important;
  color: var(--apple-white) !important;
}

.gradient-orange {
  background: var(--gradient-orange) !important;
  color: var(--apple-white) !important;
}

.gradient-glass {
  background: var(--gradient-glass) !important;
  backdrop-filter: blur(10px) !important;
}

.gradient-frosted {
  background: var(--gradient-frosted) !important;
  backdrop-filter: blur(20px) !important;
}

/* Effets de hover sympas */
.hover-lift:hover {
  transform: translateY(-4px) scale(1.02) !important;
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94) !important;
}

.hover-glow:hover {
  box-shadow: 0 0 20px rgba(139, 21, 56, 0.4) !important;
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94) !important;
}

/* Apple-style Badges */
.ant-badge {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
}

.ant-badge-count {
  background: var(--gradient-grenat) !important;
  border: 2px solid var(--apple-white) !important;
  border-radius: 12px !important;
  font-size: 11px !important;
  font-weight: 600 !important;
  min-width: 20px !important;
  height: 20px !important;
  line-height: 16px !important;
  box-shadow: var(--shadow-sm) !important;
}

.ant-badge-dot {
  background: var(--apple-grenat) !important;
  border: 2px solid var(--apple-white) !important;
  width: 8px !important;
  height: 8px !important;
  box-shadow: var(--shadow-sm) !important;
}

/* Badges colorés Apple */
.badge-success .ant-badge-count {
  background: var(--gradient-green) !important;
}

.badge-warning .ant-badge-count {
  background: var(--gradient-orange) !important;
}

.badge-error .ant-badge-count {
  background: linear-gradient(135deg, var(--apple-red) 0%, #dc3545 100%) !important;
}

.badge-info .ant-badge-count {
  background: var(--gradient-blue) !important;
}

/* Status badges */
.ant-tag {
  border-radius: var(--radius-md) !important;
  border: none !important;
  font-weight: 500 !important;
  font-size: 11px !important;
  padding: 4px 8px !important;
  box-shadow: var(--shadow-sm) !important;
}

.ant-tag-success {
  background: var(--gradient-green) !important;
  color: var(--apple-white) !important;
}

.ant-tag-warning {
  background: var(--gradient-orange) !important;
  color: var(--apple-white) !important;
}

.ant-tag-error {
  background: linear-gradient(135deg, var(--apple-red) 0%, #dc3545 100%) !important;
  color: var(--apple-white) !important;
}

.ant-tag-processing {
  background: var(--gradient-grenat) !important;
  color: var(--apple-white) !important;
}

.ant-tag-default {
  background: var(--gradient-secondary) !important;
  color: var(--apple-gray-900) !important;
}

.ant-menu-item:hover {
  background-color: var(--traiteria-primary) !important; /* Fond rouge bordeaux au hover */
  color: #ffffff !important; /* Texte blanc au hover */
}

.ant-menu-item:hover .anticon {
  color: #ffffff !important; /* Icône blanche au hover */
}

.ant-menu-item:hover a {
  color: #ffffff !important; /* Lien blanc au hover */
}

.ant-menu-item:hover span {
  color: #ffffff !important; /* Span blanc au hover */
}

.ant-menu-submenu-title {
  color: #000000d9 !important;
  background-color: transparent !important;
}

.ant-menu-submenu-title:hover {
  color: #ffffff !important; /* Texte blanc au hover */
  background-color: var(--traiteria-primary) !important; /* Fond rouge bordeaux au hover */
}

.ant-menu-submenu-title:hover .anticon {
  color: #ffffff !important; /* Icône blanche au hover */
}

.ant-menu-submenu-title:hover span {
  color: #ffffff !important; /* Span blanc au hover */
}

.ant-menu-submenu-title.ant-menu-submenu-selected {
  color: var(--traiteria-primary) !important;
}

.ant-menu-submenu-open > .ant-menu-submenu-title {
  color: var(--traiteria-primary) !important;
}

/* Styles spécifiques pour les sous-menus */
.ant-menu-sub {
  background-color: var(--traiteria-sidebar) !important;
}

.ant-menu-sub .ant-menu-item {
  color: #000000d9 !important;
  background-color: transparent !important;
  padding-left: 48px !important;
}

.ant-menu-sub .ant-menu-item-selected {
  background-color: var(--traiteria-primary) !important;
  color: #ffffff !important;
}

.ant-menu-sub .ant-menu-item-selected .anticon {
  color: #ffffff !important;
}

.ant-menu-sub .ant-menu-item-selected a {
  color: #ffffff !important;
}

.ant-menu-sub .ant-menu-item:hover {
  background-color: var(--traiteria-primary) !important; /* Fond rouge bordeaux au hover */
  color: #ffffff !important; /* Texte blanc au hover */
}

.ant-menu-sub .ant-menu-item:hover .anticon {
  color: #ffffff !important; /* Icône blanche au hover */
}

.ant-menu-sub .ant-menu-item:hover a {
  color: #ffffff !important; /* Lien blanc au hover */
}

.ant-menu-sub .ant-menu-item:hover span {
  color: #ffffff !important; /* Span blanc au hover */
}

/* Correction pour les icônes */
.ant-menu-item .anticon,
.ant-menu-submenu-title .anticon {
  color: inherit !important;
}

/* Utility classes for Traiteria colors */
.text-traiteria-primary {
  color: var(--traiteria-primary) !important;
}

.bg-traiteria-primary {
  background-color: var(--traiteria-primary) !important;
}

.text-traiteria-secondary {
  color: var(--traiteria-secondary) !important;
}

.bg-traiteria-secondary {
  background-color: var(--traiteria-secondary) !important;
}

.border-traiteria-primary {
  border-color: var(--traiteria-primary) !important;
}

/* Styles globaux pour border radius personnalisé Traiteria */
.ant-btn {
  border-radius: 8px !important;
}

.ant-card {
  border-radius: 12px !important;
}

.ant-modal-content {
  border-radius: 12px !important;
}

.ant-drawer-content {
  border-radius: 12px !important;
}

.ant-popover-content {
  border-radius: 8px !important;
}

.ant-tooltip-content {
  border-radius: 6px !important;
}

.ant-input {
  border-radius: 8px !important;
}

.ant-select-selector {
  border-radius: 8px !important;
}

.ant-table {
  border-radius: 12px !important;
}

.ant-pagination-item {
  border-radius: 8px !important;
}

.ant-tabs-tab {
  border-radius: 8px 8px 0 0 !important;
}

