@tailwind base;
@tailwind components;
@tailwind utilities;

/* Apple-inspired Global Styles */
:root {
  --apple-black: #000000;
  --apple-white: #ffffff;
  --apple-gray-50: #fafafa;
  --apple-gray-100: #f5f5f7;
  --apple-gray-200: #e5e5e7;
  --apple-gray-300: #d2d2d7;
  --apple-gray-600: #6e6e73;
  --apple-gray-900: #1d1d1f;
  --apple-blue: #007aff;
  --apple-green: #34c759;
  --apple-orange: #ff9500;
  --apple-red: #ff3b30;
  --apple-purple: #af52de;
  --apple-pink: #ff2d92;
  --apple-teal: #5ac8fa;
  --apple-indigo: #5856d6;

  /* Gradients Apple-style sympas */
  --gradient-primary: linear-gradient(135deg, var(--apple-black) 0%, #333333 100%);
  --gradient-secondary: linear-gradient(135deg, var(--apple-gray-100) 0%, var(--apple-white) 100%);
  --gradient-card: linear-gradient(135deg, var(--apple-white) 0%, var(--apple-gray-50) 100%);
  --gradient-button: linear-gradient(135deg, var(--apple-black) 0%, #2c2c2e 100%);
  --gradient-hover: linear-gradient(135deg, #333333 0%, var(--apple-black) 100%);

  /* Dégradés colorés sympas */
  --gradient-blue: linear-gradient(135deg, var(--apple-blue) 0%, #0056d6 100%);
  --gradient-green: linear-gradient(135deg, var(--apple-green) 0%, #28a745 100%);
  --gradient-purple: linear-gradient(135deg, var(--apple-purple) 0%, #8e44ad 100%);
  --gradient-pink: linear-gradient(135deg, var(--apple-pink) 0%, #e91e63 100%);
  --gradient-teal: linear-gradient(135deg, var(--apple-teal) 0%, #17a2b8 100%);
  --gradient-orange: linear-gradient(135deg, var(--apple-orange) 0%, #fd7e14 100%);

  /* Dégradés subtils pour backgrounds */
  --gradient-glass: linear-gradient(135deg, rgba(255,255,255,0.9) 0%, rgba(255,255,255,0.7) 100%);
  --gradient-frosted: linear-gradient(135deg, rgba(248,249,250,0.95) 0%, rgba(233,236,239,0.9) 100%);

  /* Shadows */
  --shadow-sm: 0 2px 8px rgba(0, 0, 0, 0.06);
  --shadow-md: 0 4px 16px rgba(0, 0, 0, 0.08);
  --shadow-lg: 0 8px 24px rgba(0, 0, 0, 0.12);
  --shadow-xl: 0 16px 32px rgba(0, 0, 0, 0.16);

  /* Border radius */
  --radius-sm: 8px;
  --radius-md: 12px;
  --radius-lg: 16px;
  --radius-xl: 20px;
}

/* Global body styling with Traiteria colors */
body {
  font-family: 'Avenir', 'Helvetica Neue', Arial, Helvetica, sans-serif;
  background-color: var(--traiteria-secondary);
}

/* Amélioration de la distinction sidebar/contenu */
.ant-layout-sider {
  border-right: 1px solid #f0f0f0 !important;
}

/* Styles pour ng-zorro menu avec couleurs Traiteria */
.ant-menu {
  background-color: var(--traiteria-sidebar) !important;
  border-right: 1px solid #f0f0f0 !important;
}

.ant-menu-item {
  color: #000000d9 !important;
  background-color: transparent !important;
}

.ant-menu-item-selected {
  background-color: var(--traiteria-primary) !important;
  color: #ffffff !important;
}

.ant-menu-item-selected .anticon {
  color: #ffffff !important;
}

.ant-menu-item-selected a {
  color: #ffffff !important;
}

.ant-menu-item-selected span {
  color: #ffffff !important;
}

/* Apple-style Menu - Clean and Modern */
.ant-menu-item.ant-menu-item-selected {
  background: var(--gradient-primary) !important;
  border-radius: var(--radius-md) !important;
  color: var(--apple-white) !important;
  margin: 4px 4px !important; /* Réduire les marges */
  box-shadow: var(--shadow-sm) !important;
  transition: all 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94) !important;
  padding-left: 12px !important; /* Réduire le padding gauche */
}

.ant-menu-item.ant-menu-item-selected a,
.ant-menu-item.ant-menu-item-selected span {
  color: var(--apple-white) !important;
  font-weight: 500 !important;
}

/* Remove default border */
.ant-menu-item.ant-menu-item-selected::after {
  border-right: none !important;
  opacity: 0 !important;
}

/* Apple-style hover effects */
.ant-menu-item:hover {
  background-color: var(--apple-gray-100) !important;
  border-radius: var(--radius-md) !important;
  margin: 4px 4px !important; /* Réduire les marges */
  transition: all 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94) !important;
  padding-left: 12px !important; /* Réduire le padding gauche */
}

.ant-menu-item:hover a,
.ant-menu-item:hover span {
  color: var(--apple-gray-900) !important; /* Texte foncé au hover */
}

.ant-menu-item.ant-menu-item-selected:hover {
  background: var(--gradient-hover) !important;
  transform: translateY(-1px) !important;
  box-shadow: var(--shadow-md) !important;
}

/* RouterLinkActive specific styles */
li.ant-menu-item.ant-menu-item-selected {
  background: var(--gradient-primary) !important;
  color: var(--apple-white) !important;
}

li.ant-menu-item.ant-menu-item-selected a {
  color: var(--apple-white) !important;
  font-weight: 500 !important;
}

/* Force style on all child elements */
.ant-menu-item.ant-menu-item-selected * {
  color: var(--apple-white) !important;
}

/* Override ng-zorro defaults */
.ant-menu-light .ant-menu-item.ant-menu-item-selected {
  background: var(--gradient-primary) !important;
  color: var(--apple-white) !important;
}

.ant-menu-light .ant-menu-item.ant-menu-item-selected a {
  color: var(--apple-white) !important;
}

.ant-menu-light .ant-menu-item.ant-menu-item-selected::after {
  border-right: none !important;
  opacity: 0 !important;
}

/* Menu container styling */
.ant-menu-light {
  background: var(--apple-white) !important;
  border-right: 1px solid var(--apple-gray-200) !important;
}

/* Liens normaux (non sélectionnés) */
.ant-menu-item a,
.ant-menu-item span {
  color: var(--apple-gray-900) !important;
  font-weight: 500 !important;
  transition: all 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94) !important;
}

/* Réduire le padding pour tous les éléments de menu */
.ant-menu-item {
  padding-left: 12px !important;
  padding-right: 8px !important;
  margin: 2px 4px !important;
}

/* Submenu styling */
.ant-menu-submenu-title:hover {
  background-color: var(--apple-gray-100) !important;
  border-radius: var(--radius-md) !important;
  margin: 4px 4px !important;
  padding-left: 12px !important;
}

.ant-menu-submenu-title:hover .ant-menu-submenu-arrow,
.ant-menu-submenu-title:hover span {
  color: var(--apple-gray-900) !important;
}

/* Sous-menus ouverts */
.ant-menu-submenu-open .ant-menu-submenu-title {
  color: var(--apple-gray-900) !important;
}

.ant-menu-submenu-open .ant-menu-submenu-title .ant-menu-submenu-arrow,
.ant-menu-submenu-open .ant-menu-submenu-title span {
  color: var(--apple-gray-900) !important;
}

/* Items de sous-menu */
.ant-menu-sub .ant-menu-item {
  padding-left: 20px !important;
  margin: 2px 8px !important;
}

.ant-menu-sub .ant-menu-item a,
.ant-menu-sub .ant-menu-item span {
  color: var(--apple-gray-600) !important;
}

.ant-menu-sub .ant-menu-item:hover a,
.ant-menu-sub .ant-menu-item:hover span {
  color: var(--apple-gray-900) !important;
}

/* Titre de sous-menu normal */
.ant-menu-submenu-title {
  color: var(--apple-gray-900) !important;
  padding-left: 12px !important;
  margin: 2px 4px !important;
}

.ant-menu-submenu-title .ant-menu-submenu-arrow,
.ant-menu-submenu-title span {
  color: var(--apple-gray-900) !important;
}

/* Force la couleur sur TOUS les éléments de menu */
.ant-menu-light .ant-menu-item,
.ant-menu-light .ant-menu-submenu-title,
.ant-menu-light .ant-menu-item a,
.ant-menu-light .ant-menu-submenu-title span,
.ant-menu-light .ant-menu-submenu-arrow {
  color: var(--apple-gray-900) !important;
}

.ant-menu-light .ant-menu-item:hover,
.ant-menu-light .ant-menu-submenu-title:hover,
.ant-menu-light .ant-menu-item:hover a,
.ant-menu-light .ant-menu-submenu-title:hover span,
.ant-menu-light .ant-menu-submenu-title:hover .ant-menu-submenu-arrow {
  color: var(--apple-gray-900) !important;
}

/* Éléments sélectionnés restent blancs */
.ant-menu-light .ant-menu-item-selected,
.ant-menu-light .ant-menu-item-selected a,
.ant-menu-light .ant-menu-item-selected span {
  color: var(--apple-white) !important;
}

/* Apple-style Form Controls */
.ant-select,
.ant-input,
.ant-input-number,
.ant-picker,
.ant-input-group {
  width: 100% !important;
  border-radius: var(--radius-md) !important;
  border: 1px solid var(--apple-gray-200) !important;
  transition: all 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94) !important;
}

.ant-input:hover,
.ant-select-selector:hover,
.ant-picker:hover {
  border-color: var(--apple-gray-300) !important;
  box-shadow: var(--shadow-sm) !important;
}

.ant-input:focus,
.ant-select-focused .ant-select-selector,
.ant-picker-focused {
  border-color: var(--apple-blue) !important;
  box-shadow: 0 0 0 2px rgba(0, 122, 255, 0.2) !important;
}

.ant-form-item-control-input {
  width: 100% !important;
}

.ant-form-item-control-input-content {
  width: 100% !important;
}

/* Apple-style Buttons */
.ant-btn-primary {
  background: var(--gradient-blue) !important;
  border: none !important;
  border-radius: var(--radius-md) !important;
  font-weight: 500 !important;
  box-shadow: var(--shadow-sm) !important;
  transition: all 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94) !important;
  color: var(--apple-white) !important;
}

.ant-btn-primary:hover {
  background: linear-gradient(135deg, #0056d6 0%, var(--apple-blue) 100%) !important;
  transform: translateY(-2px) !important;
  box-shadow: 0 8px 25px rgba(0, 122, 255, 0.3) !important;
}

.ant-btn-default {
  background: var(--apple-white) !important;
  border: 1px solid var(--apple-gray-200) !important;
  border-radius: var(--radius-md) !important;
  color: var(--apple-gray-900) !important;
  font-weight: 500 !important;
  transition: all 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94) !important;
}

.ant-btn-default:hover {
  background: var(--apple-gray-50) !important;
  border-color: var(--apple-gray-300) !important;
  transform: translateY(-1px) !important;
  box-shadow: var(--shadow-sm) !important;
}

/* Apple-style Cards */
.ant-card {
  border-radius: var(--radius-lg) !important;
  border: 1px solid var(--apple-gray-200) !important;
  box-shadow: var(--shadow-sm) !important;
  background: var(--apple-white) !important;
}

.ant-card-head {
  border-bottom: 1px solid var(--apple-gray-200) !important;
  background: var(--gradient-secondary) !important;
}

.ant-card-head-title {
  color: var(--apple-gray-900) !important;
  font-weight: 600 !important;
}

/* Apple-style Tables */
.ant-table {
  border-radius: var(--radius-lg) !important;
  overflow: hidden !important;
}

.ant-table-thead > tr > th {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%) !important;
  color: var(--apple-gray-900) !important;
  border-bottom: 1px solid var(--apple-gray-200) !important;
  font-weight: 600 !important;
  backdrop-filter: blur(10px) !important;
}

.ant-table-tbody > tr:hover > td {
  background: var(--apple-gray-50) !important;
}

/* Apple-style Modals */
.ant-modal-content {
  border-radius: var(--radius-xl) !important;
  box-shadow: var(--shadow-xl) !important;
  border: 1px solid var(--apple-gray-200) !important;
}

.ant-modal-header {
  border-bottom: 1px solid var(--apple-gray-200) !important;
  background: var(--gradient-secondary) !important;
  border-radius: var(--radius-xl) var(--radius-xl) 0 0 !important;
}

/* Apple-style Notifications */
.ant-notification {
  border-radius: var(--radius-lg) !important;
  box-shadow: var(--shadow-lg) !important;
}

/* Apple-style Steps */
.ant-steps-item-process .ant-steps-item-icon {
  background: var(--gradient-button) !important;
  border-color: var(--apple-black) !important;
}

.ant-steps-item-finish .ant-steps-item-icon {
  background: var(--apple-green) !important;
  border-color: var(--apple-green) !important;
}

/* Global Typography - Apple style */
body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif !important;
  color: var(--apple-gray-900) !important;
  background: var(--apple-gray-50) !important;
}

h1, h2, h3, h4, h5, h6 {
  color: var(--apple-black) !important;
  font-weight: 600 !important;
}

/* Smooth scrolling */
* {
  scroll-behavior: smooth;
}

/* Selection styling */
::selection {
  background: var(--apple-blue);
  color: var(--apple-white);
}

/* Classes utilitaires pour dégradés sympas */
.gradient-blue {
  background: var(--gradient-blue) !important;
  color: var(--apple-white) !important;
}

.gradient-green {
  background: var(--gradient-green) !important;
  color: var(--apple-white) !important;
}

.gradient-purple {
  background: var(--gradient-purple) !important;
  color: var(--apple-white) !important;
}

.gradient-pink {
  background: var(--gradient-pink) !important;
  color: var(--apple-white) !important;
}

.gradient-teal {
  background: var(--gradient-teal) !important;
  color: var(--apple-white) !important;
}

.gradient-orange {
  background: var(--gradient-orange) !important;
  color: var(--apple-white) !important;
}

.gradient-glass {
  background: var(--gradient-glass) !important;
  backdrop-filter: blur(10px) !important;
}

.gradient-frosted {
  background: var(--gradient-frosted) !important;
  backdrop-filter: blur(20px) !important;
}

/* Effets de hover sympas */
.hover-lift:hover {
  transform: translateY(-4px) scale(1.02) !important;
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94) !important;
}

.hover-glow:hover {
  box-shadow: 0 0 20px rgba(0, 122, 255, 0.4) !important;
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94) !important;
}

.ant-menu-item:hover {
  background-color: var(--traiteria-primary) !important; /* Fond rouge bordeaux au hover */
  color: #ffffff !important; /* Texte blanc au hover */
}

.ant-menu-item:hover .anticon {
  color: #ffffff !important; /* Icône blanche au hover */
}

.ant-menu-item:hover a {
  color: #ffffff !important; /* Lien blanc au hover */
}

.ant-menu-item:hover span {
  color: #ffffff !important; /* Span blanc au hover */
}

.ant-menu-submenu-title {
  color: #000000d9 !important;
  background-color: transparent !important;
}

.ant-menu-submenu-title:hover {
  color: #ffffff !important; /* Texte blanc au hover */
  background-color: var(--traiteria-primary) !important; /* Fond rouge bordeaux au hover */
}

.ant-menu-submenu-title:hover .anticon {
  color: #ffffff !important; /* Icône blanche au hover */
}

.ant-menu-submenu-title:hover span {
  color: #ffffff !important; /* Span blanc au hover */
}

.ant-menu-submenu-title.ant-menu-submenu-selected {
  color: var(--traiteria-primary) !important;
}

.ant-menu-submenu-open > .ant-menu-submenu-title {
  color: var(--traiteria-primary) !important;
}

/* Styles spécifiques pour les sous-menus */
.ant-menu-sub {
  background-color: var(--traiteria-sidebar) !important;
}

.ant-menu-sub .ant-menu-item {
  color: #000000d9 !important;
  background-color: transparent !important;
  padding-left: 48px !important;
}

.ant-menu-sub .ant-menu-item-selected {
  background-color: var(--traiteria-primary) !important;
  color: #ffffff !important;
}

.ant-menu-sub .ant-menu-item-selected .anticon {
  color: #ffffff !important;
}

.ant-menu-sub .ant-menu-item-selected a {
  color: #ffffff !important;
}

.ant-menu-sub .ant-menu-item:hover {
  background-color: var(--traiteria-primary) !important; /* Fond rouge bordeaux au hover */
  color: #ffffff !important; /* Texte blanc au hover */
}

.ant-menu-sub .ant-menu-item:hover .anticon {
  color: #ffffff !important; /* Icône blanche au hover */
}

.ant-menu-sub .ant-menu-item:hover a {
  color: #ffffff !important; /* Lien blanc au hover */
}

.ant-menu-sub .ant-menu-item:hover span {
  color: #ffffff !important; /* Span blanc au hover */
}

/* Correction pour les icônes */
.ant-menu-item .anticon,
.ant-menu-submenu-title .anticon {
  color: inherit !important;
}

/* Utility classes for Traiteria colors */
.text-traiteria-primary {
  color: var(--traiteria-primary) !important;
}

.bg-traiteria-primary {
  background-color: var(--traiteria-primary) !important;
}

.text-traiteria-secondary {
  color: var(--traiteria-secondary) !important;
}

.bg-traiteria-secondary {
  background-color: var(--traiteria-secondary) !important;
}

.border-traiteria-primary {
  border-color: var(--traiteria-primary) !important;
}

/* Styles globaux pour border radius personnalisé Traiteria */
.ant-btn {
  border-radius: 8px !important;
}

.ant-card {
  border-radius: 12px !important;
}

.ant-modal-content {
  border-radius: 12px !important;
}

.ant-drawer-content {
  border-radius: 12px !important;
}

.ant-popover-content {
  border-radius: 8px !important;
}

.ant-tooltip-content {
  border-radius: 6px !important;
}

.ant-input {
  border-radius: 8px !important;
}

.ant-select-selector {
  border-radius: 8px !important;
}

.ant-table {
  border-radius: 12px !important;
}

.ant-pagination-item {
  border-radius: 8px !important;
}

.ant-tabs-tab {
  border-radius: 8px 8px 0 0 !important;
}

