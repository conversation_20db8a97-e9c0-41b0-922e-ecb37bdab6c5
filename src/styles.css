@tailwind base;
@tailwind components;
@tailwind utilities;

/* Traiteria Global Styles */
:root {
  --traiteria-primary: #8A3A45;
  --traiteria-secondary: #fafafa;
  --traiteria-sidebar: #ffffff;
  --traiteria-primary-light: #A04A56;
  --traiteria-primary-dark: #742F38;
  --traiteria-secondary-dark: #f5f5f5;
}

/* Global body styling with Traiteria colors */
body {
  font-family: 'Avenir', 'Helvetica Neue', Arial, Helvetica, sans-serif;
  background-color: var(--traiteria-secondary);
}

/* Amélioration de la distinction sidebar/contenu */
.ant-layout-sider {
  border-right: 1px solid #f0f0f0 !important;
}

/* Styles pour ng-zorro menu avec couleurs Traiteria */
.ant-menu {
  background-color: var(--traiteria-sidebar) !important;
  border-right: 1px solid #f0f0f0 !important;
}

.ant-menu-item {
  color: #000000d9 !important;
  background-color: transparent !important;
}

.ant-menu-item-selected {
  background-color: var(--traiteria-primary) !important;
  color: #ffffff !important;
}

.ant-menu-item-selected .anticon {
  color: #ffffff !important;
}

.ant-menu-item-selected a {
  color: #ffffff !important;
}

.ant-menu-item-selected span {
  color: #ffffff !important;
}

/* Correction spécifique pour routerLinkActive - toute l'entrée active */
.ant-menu-item.ant-menu-item-selected,
.ant-menu-item.ant-menu-item-selected a,
.ant-menu-item.ant-menu-item-selected span {
  color: #ffffff !important;
  background-color: var(--traiteria-primary) !important;
}

/* Style pour l'élément de menu actif */
.ant-menu-item.ant-menu-item-selected {
  background-color: var(--traiteria-primary) !important;
  border-radius: 0 !important;
}

.ant-menu-item.ant-menu-item-selected::after {
  border-right: 3px solid var(--traiteria-primary) !important;
}

/* Correction globale pour que tous les champs prennent toute la largeur */
.ant-select,
.ant-input,
.ant-input-number,
.ant-picker,
.ant-input-group {
  width: 100% !important;
}

.ant-form-item-control-input {
  width: 100% !important;
}

.ant-form-item-control-input-content {
  width: 100% !important;
}

.ant-menu-item:hover {
  background-color: var(--traiteria-primary) !important; /* Fond rouge bordeaux au hover */
  color: #ffffff !important; /* Texte blanc au hover */
}

.ant-menu-item:hover .anticon {
  color: #ffffff !important; /* Icône blanche au hover */
}

.ant-menu-item:hover a {
  color: #ffffff !important; /* Lien blanc au hover */
}

.ant-menu-item:hover span {
  color: #ffffff !important; /* Span blanc au hover */
}

.ant-menu-submenu-title {
  color: #000000d9 !important;
  background-color: transparent !important;
}

.ant-menu-submenu-title:hover {
  color: #ffffff !important; /* Texte blanc au hover */
  background-color: var(--traiteria-primary) !important; /* Fond rouge bordeaux au hover */
}

.ant-menu-submenu-title:hover .anticon {
  color: #ffffff !important; /* Icône blanche au hover */
}

.ant-menu-submenu-title:hover span {
  color: #ffffff !important; /* Span blanc au hover */
}

.ant-menu-submenu-title.ant-menu-submenu-selected {
  color: var(--traiteria-primary) !important;
}

.ant-menu-submenu-open > .ant-menu-submenu-title {
  color: var(--traiteria-primary) !important;
}

/* Styles spécifiques pour les sous-menus */
.ant-menu-sub {
  background-color: var(--traiteria-sidebar) !important;
}

.ant-menu-sub .ant-menu-item {
  color: #000000d9 !important;
  background-color: transparent !important;
  padding-left: 48px !important;
}

.ant-menu-sub .ant-menu-item-selected {
  background-color: var(--traiteria-primary) !important;
  color: #ffffff !important;
}

.ant-menu-sub .ant-menu-item-selected .anticon {
  color: #ffffff !important;
}

.ant-menu-sub .ant-menu-item-selected a {
  color: #ffffff !important;
}

.ant-menu-sub .ant-menu-item:hover {
  background-color: var(--traiteria-primary) !important; /* Fond rouge bordeaux au hover */
  color: #ffffff !important; /* Texte blanc au hover */
}

.ant-menu-sub .ant-menu-item:hover .anticon {
  color: #ffffff !important; /* Icône blanche au hover */
}

.ant-menu-sub .ant-menu-item:hover a {
  color: #ffffff !important; /* Lien blanc au hover */
}

.ant-menu-sub .ant-menu-item:hover span {
  color: #ffffff !important; /* Span blanc au hover */
}

/* Correction pour les icônes */
.ant-menu-item .anticon,
.ant-menu-submenu-title .anticon {
  color: inherit !important;
}

/* Utility classes for Traiteria colors */
.text-traiteria-primary {
  color: var(--traiteria-primary) !important;
}

.bg-traiteria-primary {
  background-color: var(--traiteria-primary) !important;
}

.text-traiteria-secondary {
  color: var(--traiteria-secondary) !important;
}

.bg-traiteria-secondary {
  background-color: var(--traiteria-secondary) !important;
}

.border-traiteria-primary {
  border-color: var(--traiteria-primary) !important;
}

/* Styles globaux pour border radius personnalisé Traiteria */
.ant-btn {
  border-radius: 8px !important;
}

.ant-card {
  border-radius: 12px !important;
}

.ant-modal-content {
  border-radius: 12px !important;
}

.ant-drawer-content {
  border-radius: 12px !important;
}

.ant-popover-content {
  border-radius: 8px !important;
}

.ant-tooltip-content {
  border-radius: 6px !important;
}

.ant-input {
  border-radius: 8px !important;
}

.ant-select-selector {
  border-radius: 8px !important;
}

.ant-table {
  border-radius: 12px !important;
}

.ant-pagination-item {
  border-radius: 8px !important;
}

.ant-tabs-tab {
  border-radius: 8px 8px 0 0 !important;
}

