
// Apple-inspired Modern Theme for Traiteria
// Clean, minimal, sophisticated design with premium feel
@import "../node_modules/ng-zorro-antd/ng-zorro-antd.less";

// ===== APPLE-INSPIRED COLOR PALETTE =====
// Pure blacks and whites with subtle grays
@apple-black: #000000;           // Pure black - primary actions
@apple-white: #ffffff;           // Pure white - backgrounds
@apple-gray-50: #fafafa;         // Ultra light gray - subtle backgrounds
@apple-gray-100: #f5f5f7;        // Light gray - section backgrounds
@apple-gray-200: #e5e5e7;        // Medium light gray - borders
@apple-gray-300: #d2d2d7;        // Medium gray - disabled states
@apple-gray-600: #6e6e73;        // Dark gray - secondary text
@apple-gray-900: #1d1d1f;        // Near black - primary text

// Apple-style accent colors (minimal usage)
@apple-green: #34c759;           // Success green only
@apple-orange: #ff9500;          // Warning orange only
@apple-red: #ff3b30;             // Error red only

// ===== GRADIENTS - APPLE STYLE DOUX =====
@gradient-primary: linear-gradient(135deg, @apple-black 0%, #1c1c1e 100%);
@gradient-secondary: linear-gradient(135deg, @apple-gray-50 0%, @apple-white 100%);
@gradient-card: linear-gradient(135deg, @apple-white 0%, @apple-gray-50 100%);
@gradient-button: linear-gradient(135deg, @apple-black 0%, #1c1c1e 100%);
@gradient-hover: linear-gradient(135deg, #2c2c2e 0%, @apple-black 100%);
@gradient-soft: linear-gradient(135deg, @apple-gray-100 0%, @apple-gray-50 100%);
@gradient-subtle: linear-gradient(135deg, rgba(0,0,0,0.05) 0%, rgba(0,0,0,0.02) 100%);

// ===== CORE THEME VARIABLES =====
@primary-color: @apple-black;
@success-color: @apple-green;
@warning-color: @apple-orange;
@error-color: @apple-red;
@info-color: @apple-blue;

// ===== LAYOUT COLORS =====
@layout-body-background: @apple-gray-50;
@layout-header-background: @apple-white;
@layout-sider-background: @apple-white;

// ===== MENU STYLING - APPLE SIDEBAR =====
@menu-bg: @apple-white;
@menu-item-color: @apple-gray-900;
@menu-item-active-bg: @apple-black;
@menu-item-selected-bg: @apple-black;
@menu-item-selected-color: @apple-white;
@menu-item-hover-bg: @apple-gray-100;

// ===== BUTTON STYLING - APPLE BUTTONS =====
@btn-primary-bg: @apple-black;
@btn-primary-border: @apple-black;
@btn-default-bg: @apple-white;
@btn-default-border: @apple-gray-200;
@btn-default-color: @apple-gray-900;

// ===== TYPOGRAPHY - APPLE STYLE =====
@text-color: @apple-gray-900;
@text-color-secondary: @apple-gray-600;
@heading-color: @apple-black;
@link-color: @apple-blue;
@link-hover-color: darken(@apple-blue, 10%);

// ===== BORDERS AND BACKGROUNDS =====
@border-color-base: @apple-gray-200;
@border-color-split: @apple-gray-100;
@background-color-light: @apple-gray-50;
@background-color-base: @apple-gray-100;

// ===== COMPONENT SPECIFIC =====
@table-header-bg: @apple-gray-50;
@table-row-hover-bg: @apple-gray-50;
@card-background: @apple-white;
@card-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);

// ===== FORM ELEMENTS - APPLE STYLE =====
@input-bg: @apple-white;
@input-border-color: @apple-gray-200;
@input-hover-border-color: @apple-gray-300;
@input-focus-border-color: @apple-blue;
@input-placeholder-color: @apple-gray-600;

// ===== APPLE-INSPIRED BORDER RADIUS =====
// Subtle, consistent rounding like Apple's design system
@border-radius-base: 12px;       // Apple's preferred radius
@border-radius-sm: 8px;          // Small elements
@border-radius-lg: 16px;         // Large elements
@btn-border-radius-base: 12px;   // Buttons
@btn-border-radius-sm: 8px;      // Small buttons
@btn-border-radius-lg: 16px;     // Large buttons
@card-radius: 16px;              // Cards - more prominent
@modal-border-radius: 20px;      // Modals - Apple sheet style
@drawer-border-radius: 20px;     // Drawers
@popover-border-radius: 12px;    // Popovers
@tooltip-border-radius: 8px;     // Tooltips

// ===== SPACING - APPLE GRID SYSTEM =====
// Apple uses 8px base unit for spacing
@padding-xs: 4px;    // 0.5 * base
@padding-sm: 8px;    // 1 * base
@padding-md: 16px;   // 2 * base
@padding-lg: 24px;   // 3 * base
@padding-xl: 32px;   // 4 * base

@margin-xs: 4px;
@margin-sm: 8px;
@margin-md: 16px;
@margin-lg: 24px;
@margin-xl: 32px;

// ===== SHADOWS - APPLE DEPTH =====
@shadow-1-up: 0 -2px 8px rgba(0, 0, 0, 0.06);
@shadow-1-down: 0 2px 8px rgba(0, 0, 0, 0.06);
@shadow-2: 0 4px 16px rgba(0, 0, 0, 0.08);
@shadow-3: 0 8px 24px rgba(0, 0, 0, 0.12);
@shadow-4: 0 16px 32px rgba(0, 0, 0, 0.16);

// ===== ANIMATION - APPLE TIMING =====
@ease-base-out: cubic-bezier(0.25, 0.46, 0.45, 0.94);
@ease-base-in: cubic-bezier(0.55, 0.055, 0.675, 0.19);
@ease-out: cubic-bezier(0.215, 0.61, 0.355, 1);
@ease-in-out: cubic-bezier(0.645, 0.045, 0.355, 1);

@animation-duration-slow: 0.3s;
@animation-duration-base: 0.2s;
@animation-duration-fast: 0.1s;
