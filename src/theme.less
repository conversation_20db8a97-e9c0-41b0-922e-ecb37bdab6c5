
// Custom Theming for NG-ZORRO - Traiteria Brand
// For more information: https://ng.ant.design/docs/customize-theme/en
@import "../node_modules/ng-zorro-antd/ng-zorro-antd.less";

// Traiteria Brand Colors
@traiteria-primary: #672B32;    // Couleur principale - rouge bordeaux
@traiteria-secondary: #FCFAF4;  // Couleur secondaire - crème/beige clair

// Override ng-zorro variables with Traiteria brand colors
// View all variables: https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/components/style/themes/default.less

// Primary color - used for buttons, links, active states, etc.
@primary-color: @traiteria-primary;

// Success, warning, error colors (keeping defaults but could be customized)
@success-color: #52c41a;
@warning-color: #faad14;
@error-color: #f5222d;

// Layout colors
@layout-body-background: @traiteria-secondary;
@layout-header-background: #fff;
@layout-sider-background: @traiteria-primary;

// Menu colors
@menu-dark-bg: @traiteria-primary;
@menu-dark-submenu-bg: darken(@traiteria-primary, 10%);
@menu-dark-item-active-bg: lighten(@traiteria-primary, 10%);

// Button colors
@btn-primary-bg: @traiteria-primary;
@btn-primary-border: @traiteria-primary;

// Link colors
@link-color: @traiteria-primary;
@link-hover-color: lighten(@traiteria-primary, 10%);

// Border and background colors
@border-color-base: #d9d9d9;
@background-color-light: @traiteria-secondary;

// Text colors
@text-color: #000000d9;
@text-color-secondary: #00000073;

// Component specific colors
@table-header-bg: @traiteria-secondary;
@table-row-hover-bg: lighten(@traiteria-secondary, 50%);

// Form colors
@input-border-color: #d9d9d9;
@input-hover-border-color: @traiteria-primary;
@input-focus-border-color: @traiteria-primary;
