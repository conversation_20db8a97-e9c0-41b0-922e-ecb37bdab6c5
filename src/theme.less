
// Custom Theming for NG-ZORRO - Traiteria Brand
// For more information: https://ng.ant.design/docs/customize-theme/en
@import "../node_modules/ng-zorro-antd/ng-zorro-antd.less";

// Traiteria Brand Colors
@traiteria-primary: #9F1512;    // Couleur principale - rouge
@traiteria-secondary: #ffffff;  // Couleur secondaire - blanc

// Override ng-zorro variables with Traiteria brand colors
// View all variables: https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/components/style/themes/default.less

// Primary color - used for buttons, links, active states, etc.
@primary-color: @traiteria-primary;

// Success, warning, error colors (keeping defaults but could be customized)
@success-color: #52c41a;
@warning-color: #faad14;
@error-color: #f5222d;

// Layout colors
@layout-body-background: @traiteria-secondary;
@layout-header-background: #fff;
@layout-sider-background: #001529; // Garder la couleur sombre par défaut pour la sidebar

// Menu colors
@menu-dark-bg: #001529; // Couleur sombre par défaut pour le menu
@menu-dark-submenu-bg: #000c17; // Plus sombre pour les sous-menus
@menu-dark-item-active-bg: @traiteria-primary; // Utiliser la couleur principale pour les éléments actifs
@menu-dark-selected-item-icon-color: @traiteria-primary;
@menu-dark-selected-item-text-color: @traiteria-primary;

// Button colors
@btn-primary-bg: @traiteria-primary;
@btn-primary-border: @traiteria-primary;

// Link colors
@link-color: @traiteria-primary;
@link-hover-color: lighten(@traiteria-primary, 10%);

// Border and background colors
@border-color-base: #d9d9d9;
@background-color-light: @traiteria-secondary;

// Text colors
@text-color: #000000d9;
@text-color-secondary: #00000073;

// Component specific colors
@table-header-bg: @traiteria-secondary;
@table-row-hover-bg: lighten(@traiteria-secondary, 50%);

// Form colors
@input-border-color: #d9d9d9;
@input-hover-border-color: @traiteria-primary;
@input-focus-border-color: @traiteria-primary;
