
// Grenat Apple Theme for Traiteria - Simple et propre
@import "../node_modules/ng-zorro-antd/ng-zorro-antd.less";

// Couleurs Grenat Apple
@grenat-primary: #8B1538;
@grenat-light: #A91B47;
@grenat-dark: #6B0F2A;

// Variables ng-zorro principales
@primary-color: @grenat-primary;
@success-color: #34c759;
@warning-color: #ff9500;
@error-color: #ff3b30;
@info-color: #007aff;

// Layout
@layout-body-background: #fafafa;
@layout-header-background: #fff;
@layout-sider-background: #fff;

// Menu ng-zorro avec couleurs grenat Apple
@menu-bg: #fff;
@menu-item-color: #1d1d1f;
@menu-item-active-bg: #fdf2f5; // apple-grenat-soft
@menu-item-selected-bg: #fdf2f5; // apple-grenat-soft
@menu-item-selected-color: @grenat-primary; // apple-grenat
@menu-item-hover-bg: #f5f5f7;
@menu-item-hover-color: #1d1d1f;

// Boutons
@btn-primary-bg: @grenat-primary;
@btn-primary-border: @grenat-primary;

// Liens
@link-color: @grenat-primary;
@link-hover-color: @grenat-light;

// Tables Apple
@table-header-bg: #f5f5f7; // Gris Apple doux
@table-row-hover-bg: #fafafa;

// Couleurs de texte
@text-color: #1d1d1f;
@text-color-secondary: #6e6e73;

// Border radius Apple
@border-radius-base: 12px;
@border-radius-sm: 8px;
@border-radius-lg: 16px;
@btn-border-radius-base: 12px;
@card-radius: 16px;
