
// Custom Theming for NG-ZORRO - Traiteria Brand
// For more information: https://ng.ant.design/docs/customize-theme/en
@import "../node_modules/ng-zorro-antd/ng-zorro-antd.less";

// Traiteria Brand Colors
@traiteria-primary: #8A3A45;    // Couleur principale - rouge bordeaux
@traiteria-secondary: #ffffff;  // Couleur de fond de l'application - blanc
@traiteria-sidebar: #ffffff;    // Couleur de fond de la sidebar - blanc

// Override ng-zorro variables with Traiteria brand colors
// View all variables: https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/components/style/themes/default.less

// Primary color - used for buttons, links, active states, etc.
@primary-color: @traiteria-primary;

// Success, warning, error colors (keeping defaults but could be customized)
@success-color: #52c41a;
@warning-color: #faad14;
@error-color: #f5222d;

// Layout colors
@layout-body-background: @traiteria-secondary;
@layout-header-background: #fff;
@layout-sider-background: @traiteria-sidebar; // Couleur de fond de la sidebar

// Menu colors
@menu-bg: @traiteria-sidebar; // Couleur de fond de la sidebar pour le menu
@menu-item-color: #000000d9; // Couleur du texte des éléments de menu
@menu-item-active-bg: @traiteria-primary; // Utiliser la couleur principale pour les éléments actifs
@menu-item-selected-bg: @traiteria-primary;
@menu-item-selected-color: #fff;

// Button colors
@btn-primary-bg: @traiteria-primary;
@btn-primary-border: @traiteria-primary;

// Link colors
@link-color: @traiteria-primary;
@link-hover-color: lighten(@traiteria-primary, 10%);

// Border and background colors
@border-color-base: #d9d9d9;
@background-color-light: @traiteria-secondary;

// Text colors
@text-color: #000000d9;
@text-color-secondary: #00000073;

// Component specific colors
@table-header-bg: @traiteria-secondary;
@table-row-hover-bg: lighten(@traiteria-secondary, 50%);

// Form colors
@input-border-color: #d9d9d9;
@input-hover-border-color: @traiteria-primary;
@input-focus-border-color: @traiteria-primary;

// Border radius customization - Traiteria style
@border-radius-base: 8px;        // Radius de base plus arrondi
@border-radius-sm: 6px;          // Petit radius
@border-radius-lg: 12px;         // Grand radius
@btn-border-radius-base: 8px;    // Radius des boutons
@btn-border-radius-sm: 6px;      // Petits boutons
@btn-border-radius-lg: 12px;     // Grands boutons
@card-radius: 12px;              // Radius des cartes
@modal-border-radius: 12px;      // Radius des modales
@drawer-border-radius: 12px;     // Radius des drawers
@popover-border-radius: 8px;     // Radius des popovers
@tooltip-border-radius: 6px;     // Radius des tooltips
