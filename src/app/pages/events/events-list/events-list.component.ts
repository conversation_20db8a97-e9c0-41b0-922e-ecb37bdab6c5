import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzCardModule } from 'ng-zorro-antd/card';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzTableModule } from 'ng-zorro-antd/table';
import { NzTagModule } from 'ng-zorro-antd/tag';

@Component({
  selector: 'app-events-list',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    NzButtonModule,
    NzCardModule,
    NzIconModule,
    NzTableModule,
    NzTagModule
  ],
  template: `
    <div class="events-list-container">
      <nz-card nzTitle="Gestion des événements">
        <div class="header-actions">
          <button nz-button nzType="primary" routerLink="/events/new">
            <nz-icon nzType="plus"></nz-icon>
            Nouvel événement
          </button>
        </div>
        
        <nz-table #basicTable [nzData]="events" [nzPageSize]="10">
          <thead>
            <tr>
              <th>Type</th>
              <th>Client</th>
              <th>Date</th>
              <th>Statut</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let event of basicTable.data">
              <td>{{ event.type }}</td>
              <td>{{ event.client }}</td>
              <td>{{ event.date | date:'dd/MM/yyyy' }}</td>
              <td>
                <nz-tag [nzColor]="event.status === 'confirmed' ? 'green' : 'orange'">
                  {{ event.status }}
                </nz-tag>
              </td>
              <td>
                <button nz-button nzType="link" [routerLink]="['/events/edit', event.id]">
                  <nz-icon nzType="edit"></nz-icon>
                </button>
              </td>
            </tr>
          </tbody>
        </nz-table>
      </nz-card>
    </div>
  `,
  styles: [`
    .events-list-container {
      padding: 24px;
    }
    
    .header-actions {
      margin-bottom: 16px;
      text-align: right;
    }
  `]
})
export class EventsListComponent {
  events = [
    {
      id: 1,
      type: 'Mariage',
      client: 'Ahmed Alami',
      date: new Date('2024-07-15'),
      status: 'confirmed'
    },
    {
      id: 2,
      type: 'Séminaire',
      client: 'Entreprise XYZ',
      date: new Date('2024-07-20'),
      status: 'pending'
    }
  ];
}
