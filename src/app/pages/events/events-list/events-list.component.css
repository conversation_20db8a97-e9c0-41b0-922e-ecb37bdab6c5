.events-list-container {
  padding: 24px 32px 16px 32px; /* BEAUCOUP plus d'espace sur les côtés */
  max-width: 100%; /* Utiliser toute la largeur disponible */
  margin: 0;
}

.events-card {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border-radius: 12px;
  border: 1px solid #f0f0f0;
}

/* Header section */
.header-section {
  margin-bottom: 24px;
}

.header-actions {
  display: flex;
  gap: 12px;
  justify-content: space-between;
  align-items: center;
}

.header-actions button {
  border-radius: 8px;
  font-weight: 500;
}

.right-actions {
  display: flex;
  gap: 12px;
  align-items: center;
}

/* Filters section */
.filters-section {
  margin-bottom: 24px;
  padding: 20px;
  background: #fafafa;
  border-radius: 8px;
  border: 1px solid #f0f0f0;
}

.filters-grid {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1.5fr 1fr;
  gap: 16px;
  align-items: center;
}

.filter-item {
  width: 100%;
}

.filters-section .ant-input,
.filters-section .ant-select-selector,
.filters-section .ant-picker,
.filters-section .ant-select,
.filters-section .ant-input-group {
  border-radius: 8px;
  width: 100% !important;
}

/* Table container */
.table-container {
  margin-bottom: 16px;
}

.events-table {
  background: var(--apple-white);
  border-radius: var(--radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-md);
  border: 1px solid var(--apple-gray-200) !important;
}

/* Table styles */
.events-table .ant-table {
  border-radius: 12px;
}

.events-table .ant-table-thead > tr > th {
  background: #f5f5f7 !important; /* Gris Apple doux */
  color: #1d1d1f !important;
  font-weight: 600;
  border-bottom: 1px solid #e5e5e7;
  text-align: left;
  border-radius: 0;
}

.events-table .ant-table-thead > tr > th:first-child {
  border-top-left-radius: 12px;
}

.events-table .ant-table-thead > tr > th:last-child {
  border-top-right-radius: 12px;
}

.events-table .ant-table-tbody > tr > td {
  text-align: left;
  vertical-align: middle;
  padding: 12px 8px;
  border-bottom: 1px solid #f0f0f0;
}

.events-table .ant-table-tbody > tr:hover > td {
  background: rgba(138, 58, 69, 0.05);
}

.events-table .ant-table-tbody > tr:last-child > td:first-child {
  border-bottom-left-radius: 12px;
}

.events-table .ant-table-tbody > tr:last-child > td:last-child {
  border-bottom-right-radius: 12px;
}

/* Action buttons */
.action-buttons {
  display: flex;
  gap: 4px;
  justify-content: center;
  align-items: center;
}

.action-buttons button {
  border-radius: 6px;
}

/* Centrer les colonnes spécifiques : checkbox, catégorie, personnes, statut, actions */
.events-table .ant-table-tbody > tr > td:nth-child(1), /* Checkbox */
.events-table .ant-table-tbody > tr > td:nth-child(3), /* Catégorie */
.events-table .ant-table-tbody > tr > td:nth-child(6), /* Personnes */
.events-table .ant-table-tbody > tr > td:nth-child(9), /* Statut */
.events-table .ant-table-tbody > tr > td:last-child,   /* Actions */
.events-table .ant-table-thead > tr > th:nth-child(1), /* Header Checkbox */
.events-table .ant-table-thead > tr > th:nth-child(3), /* Header Catégorie */
.events-table .ant-table-thead > tr > th:nth-child(6), /* Header Personnes */
.events-table .ant-table-thead > tr > th:nth-child(9), /* Header Statut */
.events-table .ant-table-thead > tr > th:last-child {  /* Header Actions */
  text-align: center;
}

/* Summary section */
.summary-section {
  padding: 16px 20px;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 8px;
  border: 1px solid #dee2e6;
  margin-top: 16px;
}

.summary-stats {
  display: flex;
  gap: 32px;
  justify-content: center;
  align-items: center;
  flex-wrap: wrap;
}

.stat-item {
  font-size: 14px;
  color: #666;
}

.stat-item strong {
  color: #8A3A45;
  font-weight: 600;
}

/* No results */
.no-results {
  text-align: center;
  padding: 60px 20px;
  color: #666;
}

.no-results-icon {
  font-size: 64px;
  color: #d9d9d9;
  margin-bottom: 16px;
}

.no-results h3 {
  color: #333;
  margin-bottom: 8px;
  font-size: 18px;
}

.no-results p {
  margin-bottom: 24px;
  font-size: 14px;
  line-height: 1.5;
}

/* Tags personnalisés */
.ant-tag {
  border-radius: 6px;
  font-weight: 500;
  font-size: 12px;
  padding: 2px 8px;
}

/* Checkboxes */
.ant-checkbox-checked .ant-checkbox-inner {
  background-color: #8A3A45;
  border-color: #8A3A45;
}

.ant-checkbox:hover .ant-checkbox-inner {
  border-color: #8A3A45;
}

.ant-checkbox-indeterminate .ant-checkbox-inner {
  background-color: #8A3A45;
  border-color: #8A3A45;
}

/* Responsive */
@media (max-width: 1200px) {
  .filters-grid {
    grid-template-columns: 1fr 1fr 1fr;
    gap: 12px;
  }

  .summary-stats {
    gap: 16px;
  }
}

@media (max-width: 768px) {
  .filters-grid {
    grid-template-columns: 1fr;
    gap: 12px;
  }
}

@media (max-width: 768px) {
  .events-list-container {
    padding: 16px;
  }
  
  .header-actions {
    flex-direction: column;
    align-items: stretch;
  }
  
  .header-actions button {
    width: 100%;
  }
  
  .filters-section {
    padding: 16px;
  }
  
  .summary-stats {
    flex-direction: column;
    gap: 8px;
  }
  
  .action-buttons {
    flex-direction: column;
    gap: 2px;
  }
  
  .events-table .ant-table-tbody > tr > td {
    padding: 8px 4px;
    font-size: 12px;
  }
}

/* Amélioration des boutons */
.ant-btn-primary {
  background-color: #8A3A45;
  border-color: #8A3A45;
  border-radius: 8px;
}

.ant-btn-primary:hover {
  background-color: #A04A56;
  border-color: #A04A56;
}

.ant-btn-default {
  border-radius: 8px;
}

.ant-btn-link {
  color: #8A3A45;
}

.ant-btn-link:hover {
  color: #A04A56;
}

/* Pagination */
.ant-pagination {
  text-align: center;
  margin-top: 16px;
}

.ant-pagination-item {
  border-radius: 6px;
}

.ant-pagination-item-active {
  background-color: #8A3A45;
  border-color: #8A3A45;
}

.ant-pagination-item-active a {
  color: white;
}

/* Tooltips */
.ant-tooltip-inner {
  background-color: #8A3A45;
  border-radius: 6px;
}

.ant-tooltip-arrow::before {
  background-color: #8A3A45;
}

/* Popconfirm */
.ant-popover-inner {
  border-radius: 8px;
}

.ant-popover-buttons .ant-btn-primary {
  background-color: #8A3A45;
  border-color: #8A3A45;
}

/* Sélecteurs et inputs */
.ant-select-focused .ant-select-selector,
.ant-input:focus,
.ant-picker:focus {
  border-color: #8A3A45;
  box-shadow: 0 0 0 2px rgba(138, 58, 69, 0.1);
}

/* Table bordered */
.ant-table-bordered .ant-table-container {
  border: 2px solid #f0f0f0;
  border-radius: 12px;
}

.ant-table-bordered .ant-table-thead > tr > th,
.ant-table-bordered .ant-table-tbody > tr > td {
  border-right: 1px solid #f0f0f0;
}

.ant-table-bordered .ant-table-thead > tr > th:last-child,
.ant-table-bordered .ant-table-tbody > tr > td:last-child {
  border-right: none;
}

/* Pagination avec marge à droite */
.events-table .ant-pagination {
  margin-right: 16px !important;
  padding: 16px 0 !important;
}

/* Sélecteur de taille de page avec marge à droite */
.events-table .ant-pagination .ant-pagination-options {
  margin-right: 16px !important;
}

.events-table .ant-pagination .ant-pagination-options .ant-select {
  margin-right: 8px !important;
}
