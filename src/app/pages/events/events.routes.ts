import { Routes } from '@angular/router';

export const EVENTS_ROUTES: Routes = [
  {
    path: '',
    loadComponent: () => import('./events-list/events-list.component').then(m => m.EventsListComponent)
  },
  {
    path: 'new',
    loadComponent: () => import('../../components/event-form/event-form.component').then(m => m.EventFormComponent)
  },
  {
    path: 'edit/:id',
    loadComponent: () => import('../../components/event-form/event-form.component').then(m => m.EventFormComponent)
  }
];
