:host {
  display: flex;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.app-layout {
  height: 100vh;
}

.menu-sidebar {
  position: relative;
  z-index: 10;
  min-height: 100vh;
  box-shadow: 2px 0 6px rgba(0,21,41,.35);
  background-color: #EBE6DD !important; /* Couleur de fond de la sidebar */
}

.header-trigger {
  height: 64px;
  padding: 20px 24px;
  font-size: 20px;
  cursor: pointer;
  transition: all .3s,padding 0s;
}

.trigger:hover {
  color: #9F1512; /* Traiteria primary color */
}

.sidebar-logo {
  position: relative;
  height: 64px;
  padding-left: 24px;
  padding-right: 24px;
  overflow: hidden;
  background: #EBE6DD; /* Couleur de fond de la sidebar */
  transition: all .3s;
  display: flex;
  align-items: center;
}

.sidebar-logo a {
  display: flex;
  align-items: center;
  text-decoration: none;
  width: 100%;
}

/* Styles spécifiques pour le logo selon l'état de la sidebar */
.ant-layout-sider-collapsed .sidebar-logo img {
  max-width: 32px;
}

.ant-layout-sider:not(.ant-layout-sider-collapsed) .sidebar-logo img {
  max-width: 120px;
}

.sidebar-logo img {
  display: inline-block;
  height: 32px;
  width: auto;
  max-width: 120px; /* Augmenter la largeur max pour le logo complet */
  vertical-align: middle;
  transition: all .3s;
  object-fit: contain; /* Maintenir les proportions */
}

.sidebar-logo h1 {
  margin: 0 0 0 12px;
  color: #000; /* Couleur sombre pour le texte sur fond clair */
  font-weight: 600;
  font-size: 16px;
  font-family: Avenir,Helvetica Neue,Arial,Helvetica,sans-serif;
  transition: all .3s;
  white-space: nowrap;
  flex-shrink: 0;
}

nz-header {
  padding: 0;
  width: 100%;
  z-index: 2;
}

.app-header {
  position: relative;
  height: 64px;
  padding: 0;
  background: #fff;
  box-shadow: 0 1px 4px rgba(0,21,41,.08);
}

nz-content {
  margin: 24px;
}

.inner-content {
  padding: 24px;
  background: #FCFAF4; /* Couleur de fond de l'application */
  height: 100%;
}
