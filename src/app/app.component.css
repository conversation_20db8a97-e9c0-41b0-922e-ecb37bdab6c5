:host {
  display: flex;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.app-layout {
  height: 100vh;
}

.menu-sidebar {
  position: relative;
  z-index: 10;
  min-height: 100vh;
  box-shadow: 2px 0 6px rgba(0,21,41,.35);
  background-color: #ffffff !important; /* Couleur de fond de la sidebar - blanc */
}

.header-trigger {
  height: 64px;
  padding: 20px 24px;
  font-size: 20px;
  cursor: pointer;
  transition: all .3s,padding 0s;
}

.trigger:hover {
  color: #8A3A45; /* Traiteria primary color */
}

.sidebar-logo {
  position: relative;
  height: 80px; /* Augmenter la hauteur pour un logo plus grand */
  padding: 16px 24px;
  overflow: hidden;
  background: #ffffff; /* Couleur de fond de la sidebar - blanc */
  transition: all .3s;
  display: flex;
  align-items: center;
  justify-content: center; /* Centrer le contenu */
}

.sidebar-logo a {
  display: flex;
  align-items: center;
  justify-content: center; /* Centrer le logo */
  text-decoration: none;
  width: 100%;
  flex-direction: column; /* Empiler logo et texte verticalement quand ouvert */
}

/* Styles spécifiques pour le logo selon l'état de la sidebar */
.ant-layout-sider-collapsed .sidebar-logo {
  height: 64px;
  padding: 16px 8px;
}

.ant-layout-sider-collapsed .sidebar-logo a {
  flex-direction: row; /* Logo horizontal quand fermé */
}

.ant-layout-sider-collapsed .sidebar-logo img {
  height: 32px;
  max-width: 32px;
}

.ant-layout-sider:not(.ant-layout-sider-collapsed) .sidebar-logo img {
  height: 48px; /* Logo plus grand quand ouvert */
  max-width: 150px;
}

.sidebar-logo img {
  display: block;
  width: auto;
  transition: all .3s;
  object-fit: contain; /* Maintenir les proportions */
  margin: 0 auto; /* Centrer le logo */
}

.sidebar-logo h1 {
  margin: 8px 0 0 0; /* Marge en haut pour séparer du logo */
  color: #8A3A45; /* Couleur principale pour le texte */
  font-weight: 600;
  font-size: 14px;
  font-family: Avenir,Helvetica Neue,Arial,Helvetica,sans-serif;
  transition: all .3s;
  white-space: nowrap;
  text-align: center;
}

/* Masquer le texte quand la sidebar est fermée */
.ant-layout-sider-collapsed .sidebar-logo h1 {
  display: none;
}

nz-header {
  padding: 0;
  width: 100%;
  z-index: 2;
}

.app-header {
  position: relative;
  height: 64px;
  padding: 0;
  background: #fff;
  box-shadow: 0 1px 4px rgba(0,21,41,.08);
}

nz-content {
  margin: 24px;
}

.inner-content {
  padding: 24px;
  background: #ffffff; /* Couleur de fond de l'application - blanc */
  height: 100%;
}
