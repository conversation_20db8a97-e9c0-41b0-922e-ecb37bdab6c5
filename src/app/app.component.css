:host {
  display: flex;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.app-layout {
  height: 100vh;
  background: var(--apple-gray-50);
}

.menu-sidebar {
  position: fixed !important;
  left: 0;
  top: 0;
  bottom: 0;
  z-index: 10;
  height: 100vh !important;
  overflow-y: auto;
  box-shadow: var(--shadow-lg) !important;
  background: var(--apple-white) !important;
  border-right: 1px solid var(--apple-gray-200) !important;
}

.header-trigger {
  height: 64px;
  padding: 20px 24px;
  font-size: 20px;
  cursor: pointer;
  transition: all 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  color: var(--apple-gray-600);
}

.trigger:hover {
  color: var(--apple-black);
  transform: scale(1.05);
}

.sidebar-logo {
  position: relative;
  height: 80px;
  padding: 16px 24px;
  overflow: hidden;
  background: var(--apple-white);
  transition: all 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  display: flex;
  align-items: center;
  justify-content: center;
  border-bottom: 1px solid var(--apple-gray-100);
}

.sidebar-logo a {
  display: flex;
  align-items: center;
  justify-content: center; /* Centrer le logo */
  text-decoration: none;
  width: 100%;
}

/* Styles spécifiques pour le logo selon l'état de la sidebar */
.ant-layout-sider-collapsed .sidebar-logo {
  height: 64px;
  padding: 16px 8px;
}

/* Pas de changement de direction nécessaire */

.ant-layout-sider-collapsed .sidebar-logo img {
  height: 32px;
  max-width: 32px;
}

.ant-layout-sider:not(.ant-layout-sider-collapsed) .sidebar-logo img {
  height: 48px; /* Logo plus grand quand ouvert */
  max-width: 150px;
}

.sidebar-logo img {
  display: block;
  width: auto;
  transition: all .3s;
  object-fit: contain; /* Maintenir les proportions */
  margin: 0 auto; /* Centrer le logo */
}

/* Styles h1 supprimés car plus de texte */

nz-header {
  padding: 0;
  width: 100%;
  z-index: 2;
}

.app-header {
  position: relative;
  height: 64px;
  padding: 0;
  background: var(--apple-white);
  box-shadow: var(--shadow-sm);
  border-bottom: 1px solid var(--apple-gray-100);
}

/* Layout principal qui s'adapte à la sidebar fixe */
.main-content {
  margin-left: 256px; /* Largeur de la sidebar ouverte */
  transition: margin-left 0.3s ease;
  min-height: 100vh;
}

.main-content.collapsed {
  margin-left: 80px; /* Largeur de la sidebar fermée */
}

nz-content {
  margin: 0;
  background: var(--apple-gray-50);
  min-height: calc(100vh - 64px);
  overflow-y: auto;
}

.inner-content {
  padding: 0;
  background: var(--apple-gray-50);
  min-height: calc(100vh - 64px);
}

/* Responsive pour mobile */
@media (max-width: 768px) {
  .menu-sidebar {
    position: fixed !important;
    left: -256px; /* Caché par défaut sur mobile */
    transition: left 0.3s ease;
  }

  .menu-sidebar.ant-layout-sider-collapsed {
    left: -80px;
  }

  .main-content {
    margin-left: 0 !important;
  }

  /* Overlay pour mobile quand sidebar ouverte */
  .main-content::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.45);
    z-index: 5;
    display: none;
  }
}
