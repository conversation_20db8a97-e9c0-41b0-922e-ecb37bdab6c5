<div class="event-form-container">
  <nz-card nzTitle="Création d'un nouvel événement" class="form-card">
    
    <!-- Steps Navigation -->
    <nz-steps [nzCurrent]="currentStep()" nzSize="small" class="steps-nav">
      <nz-step nzTitle="Type d'événement"></nz-step>
      <nz-step nzTitle="Client"></nz-step>
      <nz-step nzTitle="Détails"></nz-step>
      <nz-step nzTitle="Paiement"></nz-step>
      <nz-step nzTitle="Finalisation"></nz-step>
    </nz-steps>

    <nz-divider></nz-divider>

    <!-- Étape 1: Type d'événement -->
    @if (currentStep() === 0) {
      <div class="step-content">
      <h3>Choisissez le type d'événement</h3>
      
      <form [formGroup]="step1Form" nz-form nzLayout="vertical">
        <!-- Choix <PERSON>ulier/Entreprise -->
        <nz-form-item>
          <nz-form-label nzRequired>Catégorie d'événement</nz-form-label>
          <nz-form-control nzErrorTip="Veuillez choisir une catégorie">
            <nz-radio-group formControlName="category" (ngModelChange)="onCategoryChange()" class="category-choice">
              <div class="category-cards">
                <div class="category-card" [class.selected]="step1Form.get('category')?.value === 'particulier'" (click)="selectCategory('particulier')">
                  <input type="radio" name="category" value="particulier" style="display: none;">
                  <div class="card-content">
                    <nz-icon nzType="user" nzTheme="outline" class="category-icon"></nz-icon>
                    <h4>Particulier</h4>
                    <p>Événements privés et familiaux</p>
                  </div>
                </div>
                <div class="category-card" [class.selected]="step1Form.get('category')?.value === 'entreprise'" (click)="selectCategory('entreprise')">
                  <input type="radio" name="category" value="entreprise" style="display: none;">
                  <div class="card-content">
                    <nz-icon nzType="bank" nzTheme="outline" class="category-icon"></nz-icon>
                    <h4>Entreprise</h4>
                    <p>Événements professionnels</p>
                  </div>
                </div>
              </div>
            </nz-radio-group>
          </nz-form-control>
        </nz-form-item>

        <!-- Type d'événement spécifique -->
        <nz-form-item *ngIf="step1Form.get('category')?.value">
          <nz-form-label nzRequired>Type d'événement</nz-form-label>
          <nz-form-control nzErrorTip="Veuillez choisir un type d'événement">
            <nz-select formControlName="eventType" nzPlaceHolder="Sélectionnez le type d'événement">
              <nz-option 
                *ngFor="let type of filteredEventTypes" 
                [nzValue]="type.value" 
                [nzLabel]="type.label">
              </nz-option>
            </nz-select>
          </nz-form-control>
        </nz-form-item>
      </form>
    </div>

    <!-- Étape 2: Client -->
    <div *ngIf="currentStep() === 1" class="step-content">
      <h3>Informations client</h3>
      
      <form [formGroup]="step2Form" nz-form nzLayout="vertical">
        <nz-form-item>
          <nz-form-label>Type de client</nz-form-label>
          <nz-form-control>
            <nz-radio-group formControlName="clientType" (ngModelChange)="onClientTypeChange()">
              <label nz-radio nzValue="existing">Client existant</label>
              <label nz-radio nzValue="new">Nouveau client</label>
            </nz-radio-group>
          </nz-form-control>
        </nz-form-item>

        <!-- Client existant -->
        <nz-form-item *ngIf="!isClientTypeNew">
          <nz-form-label nzRequired>Sélectionner un client</nz-form-label>
          <nz-form-control nzErrorTip="Veuillez sélectionner un client">
            <nz-select formControlName="selectedClient" nzPlaceHolder="Choisir un client">
              <nz-option 
                *ngFor="let client of existingClients" 
                [nzValue]="client.id" 
                [nzLabel]="client.nom + ' ' + client.prenom">
              </nz-option>
            </nz-select>
          </nz-form-control>
        </nz-form-item>

        <!-- Bouton nouveau client -->
        <nz-form-item *ngIf="!isClientTypeNew">
          <nz-form-control>
            <button nz-button nzType="dashed" (click)="openClientDrawer()" class="new-client-btn">
              <nz-icon nzType="plus"></nz-icon>
              Ajouter un nouveau client
            </button>
          </nz-form-control>
        </nz-form-item>

        <!-- Formulaire nouveau client inline -->
        <div *ngIf="isClientTypeNew" formGroupName="newClient">
          <nz-form-item>
            <nz-form-label nzRequired>Nom</nz-form-label>
            <nz-form-control nzErrorTip="Le nom est requis">
              <input nz-input formControlName="nom" placeholder="Nom du client" />
            </nz-form-control>
          </nz-form-item>

          <nz-form-item>
            <nz-form-label nzRequired>Prénom</nz-form-label>
            <nz-form-control nzErrorTip="Le prénom est requis">
              <input nz-input formControlName="prenom" placeholder="Prénom du client" />
            </nz-form-control>
          </nz-form-item>

          <div nz-row [nzGutter]="16">
            <div nz-col nzSpan="12">
              <nz-form-item>
                <nz-form-label nzRequired>CIN</nz-form-label>
                <nz-form-control nzErrorTip="Le CIN est requis">
                  <input nz-input formControlName="cin" placeholder="CIN" />
                </nz-form-control>
              </nz-form-item>
            </div>
            <div nz-col nzSpan="12">
              <nz-form-item>
                <nz-form-label nzRequired>Ville</nz-form-label>
                <nz-form-control nzErrorTip="La ville est requise">
                  <input nz-input formControlName="ville" placeholder="Ville" />
                </nz-form-control>
              </nz-form-item>
            </div>
          </div>

          <nz-form-item>
            <nz-form-label nzRequired>Adresse</nz-form-label>
            <nz-form-control nzErrorTip="L'adresse est requise">
              <textarea nz-input formControlName="adresse" placeholder="Adresse complète" rows="2"></textarea>
            </nz-form-control>
          </nz-form-item>

          <div nz-row [nzGutter]="16">
            <div nz-col nzSpan="12">
              <nz-form-item>
                <nz-form-label nzRequired>Téléphone 1</nz-form-label>
                <nz-form-control nzErrorTip="Le téléphone est requis">
                  <input nz-input formControlName="telephone1" placeholder="Téléphone principal" />
                </nz-form-control>
              </nz-form-item>
            </div>
            <div nz-col nzSpan="12">
              <nz-form-item>
                <nz-form-label>Téléphone 2</nz-form-label>
                <nz-form-control>
                  <input nz-input formControlName="telephone2" placeholder="Téléphone secondaire" />
                </nz-form-control>
              </nz-form-item>
            </div>
          </div>

          <nz-form-item>
            <nz-form-label nzRequired>Email</nz-form-label>
            <nz-form-control nzErrorTip="L'email est requis">
              <input nz-input formControlName="email" placeholder="Adresse email" type="email" />
            </nz-form-control>
          </nz-form-item>
        </div>
      </form>
    </div>

    <!-- Étape 3: Détails événement -->
    <div *ngIf="currentStep() === 2" class="step-content">
      <h3>Détails de l'événement</h3>
      
      <form [formGroup]="step3Form" nz-form nzLayout="vertical">
        <!-- Informations communes -->
        <div class="section">
          <h4>Informations générales</h4>
          
          <div nz-row [nzGutter]="16">
            <div nz-col nzSpan="8">
              <nz-form-item>
                <nz-form-label nzRequired>Nombre de personnes</nz-form-label>
                <nz-form-control nzErrorTip="Le nombre de personnes est requis">
                  <nz-input-number formControlName="nbPersonnes" [nzMin]="1" [nzMax]="10000" nzPlaceHolder="Nombre"></nz-input-number>
                </nz-form-control>
              </nz-form-item>
            </div>
            <div nz-col nzSpan="8">
              <nz-form-item>
                <nz-form-label nzRequired>Date</nz-form-label>
                <nz-form-control nzErrorTip="La date est requise">
                  <nz-date-picker formControlName="date" nzPlaceHolder="Sélectionner la date"></nz-date-picker>
                </nz-form-control>
              </nz-form-item>
            </div>
            <div nz-col nzSpan="8">
              <nz-form-item>
                <nz-form-label nzRequired>Ville</nz-form-label>
                <nz-form-control nzErrorTip="La ville est requise">
                  <input nz-input formControlName="ville" placeholder="Ville de l'événement" />
                </nz-form-control>
              </nz-form-item>
            </div>
          </div>

          <nz-form-item>
            <nz-form-label nzRequired>Adresse du lieu</nz-form-label>
            <nz-form-control nzErrorTip="L'adresse est requise">
              <textarea nz-input formControlName="adresse" placeholder="Adresse complète du lieu" rows="2"></textarea>
            </nz-form-control>
          </nz-form-item>
        </div>

        <!-- Informations spéciales pour mariage -->
        <div *ngIf="selectedEventType === 'mariage'" class="section special-section">
          <h4>Options spéciales - Mariage</h4>
          
          <nz-form-item>
            <nz-form-label>Packs</nz-form-label>
            <nz-form-control>
              <nz-select formControlName="packs" nzMode="multiple" nzPlaceHolder="Sélectionner les packs">
                <nz-option *ngFor="let pack of packOptions.mariage" [nzValue]="pack" [nzLabel]="pack"></nz-option>
              </nz-select>
            </nz-form-control>
          </nz-form-item>

          <div nz-row [nzGutter]="16">
            <div nz-col nzSpan="8">
              <nz-form-item>
                <nz-form-control>
                  <label nz-checkbox formControlName="photographe">Photographe</label>
                </nz-form-control>
              </nz-form-item>
              <nz-form-item *ngIf="step3Form.get('photographe')?.value">
                <nz-form-control>
                  <label nz-checkbox formControlName="photographeCelebre">Photographe célèbre</label>
                </nz-form-control>
              </nz-form-item>
            </div>
            <div nz-col nzSpan="8">
              <nz-form-item>
                <nz-form-control>
                  <label nz-checkbox formControlName="dj">DJ</label>
                </nz-form-control>
              </nz-form-item>
              <nz-form-item>
                <nz-form-control>
                  <label nz-checkbox formControlName="issawa">Issawa</label>
                </nz-form-control>
              </nz-form-item>
            </div>
            <div nz-col nzSpan="8">
              <nz-form-item>
                <nz-form-control>
                  <label nz-checkbox formControlName="deqaMarrakchia">Deqa Marrakchia</label>
                </nz-form-control>
              </nz-form-item>
              <nz-form-item>
                <nz-form-control>
                  <label nz-checkbox formControlName="ftour">Ftour</label>
                </nz-form-control>
              </nz-form-item>
            </div>
          </div>

          <div nz-row [nzGutter]="16">
            <div nz-col nzSpan="12">
              <nz-form-item>
                <nz-form-control>
                  <label nz-checkbox formControlName="pieceMontee">Pièce montée</label>
                </nz-form-control>
              </nz-form-item>
              <nz-form-item>
                <nz-form-control>
                  <label nz-checkbox formControlName="decoration">Décoration</label>
                </nz-form-control>
              </nz-form-item>
              <nz-form-item *ngIf="step3Form.get('decoration')?.value">
                <nz-form-label>Type de décoration</nz-form-label>
                <nz-form-control>
                  <nz-radio-group formControlName="decorationType">
                    <label nz-radio nzValue="lumiere">Lumière</label>
                    <label nz-radio nzValue="florale">Florale</label>
                  </nz-radio-group>
                </nz-form-control>
              </nz-form-item>
            </div>
            <div nz-col nzSpan="12">
              <nz-form-item>
                <nz-form-label>Type de matériel</nz-form-label>
                <nz-form-control>
                  <nz-radio-group formControlName="typeMateriel">
                    <label nz-radio nzValue="standard">Standard</label>
                    <label nz-radio nzValue="luxe">Luxe</label>
                  </nz-radio-group>
                </nz-form-control>
              </nz-form-item>
              <nz-form-item>
                <nz-form-label>Thème</nz-form-label>
                <nz-form-control>
                  <input nz-input formControlName="theme" placeholder="Thème de l'événement" />
                </nz-form-control>
              </nz-form-item>
            </div>
          </div>
        </div>

        <!-- Informations spéciales pour fête de travail -->
        <div *ngIf="selectedEventType === 'fete-travail'" class="section special-section">
          <h4>Options spéciales - Fête de travail</h4>
          
          <nz-form-item>
            <nz-form-label>Packs</nz-form-label>
            <nz-form-control>
              <nz-select formControlName="packs" nzMode="multiple" nzPlaceHolder="Sélectionner les packs">
                <nz-option *ngFor="let pack of packOptions.feteTravail" [nzValue]="pack" [nzLabel]="pack"></nz-option>
              </nz-select>
            </nz-form-control>
          </nz-form-item>

          <!-- Options similaires au mariage mais adaptées -->
          <div nz-row [nzGutter]="16">
            <div nz-col nzSpan="8">
              <nz-form-item>
                <nz-form-control>
                  <label nz-checkbox formControlName="photographe">Photographe</label>
                </nz-form-control>
              </nz-form-item>
              <nz-form-item *ngIf="step3Form.get('photographe')?.value">
                <nz-form-control>
                  <label nz-checkbox formControlName="photographeCelebre">Photographe célèbre</label>
                </nz-form-control>
              </nz-form-item>
            </div>
            <div nz-col nzSpan="8">
              <nz-form-item>
                <nz-form-control>
                  <label nz-checkbox formControlName="dj">DJ</label>
                </nz-form-control>
              </nz-form-item>
              <nz-form-item>
                <nz-form-control>
                  <label nz-checkbox formControlName="issawa">Issawa</label>
                </nz-form-control>
              </nz-form-item>
            </div>
            <div nz-col nzSpan="8">
              <nz-form-item>
                <nz-form-control>
                  <label nz-checkbox formControlName="deqaMarrakchia">Deqa Marrakchia</label>
                </nz-form-control>
              </nz-form-item>
              <nz-form-item>
                <nz-form-control>
                  <label nz-checkbox formControlName="ftour">Ftour</label>
                </nz-form-control>
              </nz-form-item>
            </div>
          </div>

          <div nz-row [nzGutter]="16">
            <div nz-col nzSpan="12">
              <nz-form-item>
                <nz-form-control>
                  <label nz-checkbox formControlName="decoration">Décoration</label>
                </nz-form-control>
              </nz-form-item>
              <nz-form-item *ngIf="step3Form.get('decoration')?.value">
                <nz-form-label>Type de décoration</nz-form-label>
                <nz-form-control>
                  <nz-radio-group formControlName="decorationType">
                    <label nz-radio nzValue="lumiere">Lumière</label>
                    <label nz-radio nzValue="florale">Florale</label>
                  </nz-radio-group>
                </nz-form-control>
              </nz-form-item>
            </div>
            <div nz-col nzSpan="12">
              <nz-form-item>
                <nz-form-label>Type de matériel</nz-form-label>
                <nz-form-control>
                  <nz-radio-group formControlName="typeMateriel">
                    <label nz-radio nzValue="standard">Standard</label>
                    <label nz-radio nzValue="luxe">Luxe</label>
                  </nz-radio-group>
                </nz-form-control>
              </nz-form-item>
              <nz-form-item>
                <nz-form-label>Thème</nz-form-label>
                <nz-form-control>
                  <input nz-input formControlName="theme" placeholder="Thème de l'événement" />
                </nz-form-control>
              </nz-form-item>
            </div>
          </div>
        </div>
      </form>
    </div>

    <!-- Étape 4: Paiement -->
    <div *ngIf="currentStep() === 3" class="step-content">
      <h3>Informations de paiement</h3>

      <form [formGroup]="step4Form" nz-form nzLayout="vertical">
        <div nz-row [nzGutter]="16">
          <div nz-col nzSpan="12">
            <nz-form-item>
              <nz-form-label nzRequired>Statut du paiement</nz-form-label>
              <nz-form-control nzErrorTip="Le statut est requis">
                <nz-select formControlName="statut" nzPlaceHolder="Statut du paiement">
                  <nz-option nzValue="non-paye" nzLabel="Non payé"></nz-option>
                  <nz-option nzValue="paye" nzLabel="Payé"></nz-option>
                  <nz-option nzValue="partiellement-paye" nzLabel="Partiellement payé"></nz-option>
                  <nz-option nzValue="en-attente" nzLabel="En attente"></nz-option>
                </nz-select>
              </nz-form-control>
            </nz-form-item>
          </div>
          <div nz-col nzSpan="12">
            <nz-form-item>
              <nz-form-label nzRequired>Montant total (DH)</nz-form-label>
              <nz-form-control nzErrorTip="Le montant total est requis">
                <nz-input-number
                  formControlName="montantTotal"
                  [nzMin]="0"
                  [nzStep]="100"
                  nzPlaceHolder="Montant total">
                </nz-input-number>
              </nz-form-control>
            </nz-form-item>
          </div>
        </div>

        <div nz-row [nzGutter]="16" *ngIf="step4Form.get('statut')?.value === 'partiellement-paye'">
          <div nz-col nzSpan="12">
            <nz-form-item>
              <nz-form-label nzRequired>Montant payé (DH)</nz-form-label>
              <nz-form-control nzErrorTip="Le montant payé est requis">
                <nz-input-number
                  formControlName="montantPaye"
                  [nzMin]="0"
                  [nzStep]="100"
                  nzPlaceHolder="Montant déjà payé">
                </nz-input-number>
              </nz-form-control>
            </nz-form-item>
          </div>
          <div nz-col nzSpan="12">
            <nz-form-item>
              <nz-form-label>Date d'échéance</nz-form-label>
              <nz-form-control>
                <nz-date-picker formControlName="dateEcheance" nzPlaceHolder="Date limite de paiement"></nz-date-picker>
              </nz-form-control>
            </nz-form-item>
          </div>
        </div>

        <nz-form-item *ngIf="step4Form.get('statut')?.value !== 'non-paye'">
          <nz-form-label>Méthode de paiement</nz-form-label>
          <nz-form-control>
            <nz-select formControlName="methodePaiement" nzPlaceHolder="Comment le client a-t-il payé ?">
              <nz-option nzValue="especes" nzLabel="Espèces"></nz-option>
              <nz-option nzValue="cheque" nzLabel="Chèque"></nz-option>
              <nz-option nzValue="virement" nzLabel="Virement bancaire"></nz-option>
              <nz-option nzValue="carte" nzLabel="Carte bancaire"></nz-option>
              <nz-option nzValue="mobile" nzLabel="Paiement mobile"></nz-option>
            </nz-select>
          </nz-form-control>
        </nz-form-item>

        <nz-form-item>
          <nz-form-label>Commentaire sur le paiement</nz-form-label>
          <nz-form-control>
            <textarea
              nz-input
              formControlName="commentairePaiement"
              placeholder="Notes sur les conditions de paiement, accords spéciaux, etc."
              rows="3">
            </textarea>
          </nz-form-control>
        </nz-form-item>

        <!-- Résumé financier -->
        <nz-card nzTitle="Résumé financier" nzSize="small" class="financial-summary">
          <div class="summary-row">
            <span>Montant total:</span>
            <strong>{{ step4Form.get('montantTotal')?.value || 0 }} DH</strong>
          </div>
          <div class="summary-row" *ngIf="step4Form.get('statut')?.value === 'partiellement-paye'">
            <span>Montant payé:</span>
            <strong class="paid-amount">{{ step4Form.get('montantPaye')?.value || 0 }} DH</strong>
          </div>
          <div class="summary-row" *ngIf="step4Form.get('statut')?.value === 'partiellement-paye'">
            <span>Reste à payer:</span>
            <strong class="remaining-amount">
              {{ (step4Form.get('montantTotal')?.value || 0) - (step4Form.get('montantPaye')?.value || 0) }} DH
            </strong>
          </div>
        </nz-card>
      </form>
    </div>

    <!-- Étape 5: Source et commentaires -->
    <div *ngIf="currentStep() === 4" class="step-content">
      <h3>Finalisation</h3>

      <form [formGroup]="step5Form" nz-form nzLayout="vertical">
        <nz-form-item>
          <nz-form-label nzRequired>Source de trafic</nz-form-label>
          <nz-form-control nzErrorTip="La source de trafic est requise">
            <nz-select formControlName="sourceTraffic" nzPlaceHolder="Comment le client nous a-t-il trouvé ?">
              <nz-option nzValue="bouche-a-oreille" nzLabel="Bouche à oreille"></nz-option>
              <nz-option nzValue="facebook" nzLabel="Facebook"></nz-option>
              <nz-option nzValue="instagram" nzLabel="Instagram"></nz-option>
              <nz-option nzValue="google" nzLabel="Recherche Google"></nz-option>
              <nz-option nzValue="site-web" nzLabel="Site web"></nz-option>
              <nz-option nzValue="flyer" nzLabel="Flyer/Prospectus"></nz-option>
              <nz-option nzValue="partenaire" nzLabel="Partenaire"></nz-option>
              <nz-option nzValue="ancien-client" nzLabel="Ancien client"></nz-option>
              <nz-option nzValue="autre" nzLabel="Autre"></nz-option>
            </nz-select>
          </nz-form-control>
        </nz-form-item>

        <nz-form-item>
          <nz-form-label>Commentaires généraux</nz-form-label>
          <nz-form-control>
            <textarea
              nz-input
              formControlName="commentaires"
              placeholder="Notes supplémentaires, demandes spéciales, observations..."
              rows="4">
            </textarea>
          </nz-form-control>
        </nz-form-item>

        <!-- Récapitulatif de l'événement -->
        <nz-card nzTitle="Récapitulatif de l'événement" nzSize="small" class="event-summary">
          <div class="summary-section">
            <h5>Type d'événement</h5>
            <p>{{ step1Form.get('category')?.value | titlecase }} - {{ step1Form.get('eventType')?.value }}</p>
          </div>

          <div class="summary-section" *ngIf="step2Form.get('clientType')?.value === 'existing'">
            <h5>Client</h5>
            <p>Client existant sélectionné</p>
          </div>

          <div class="summary-section" *ngIf="step2Form.get('clientType')?.value === 'new'">
            <h5>Nouveau client</h5>
            <p>{{ step2Form.get('newClient.nom')?.value }} {{ step2Form.get('newClient.prenom')?.value }}</p>
          </div>

          <div class="summary-section">
            <h5>Détails</h5>
            <p>{{ step3Form.get('nbPersonnes')?.value }} personnes - {{ step3Form.get('ville')?.value }}</p>
            <p>Date: {{ step3Form.get('date')?.value | date:'dd/MM/yyyy' }}</p>
          </div>

          <div class="summary-section">
            <h5>Paiement</h5>
            <p>{{ step4Form.get('montantTotal')?.value }} DH - {{ step4Form.get('statut')?.value }}</p>
          </div>
        </nz-card>
      </form>
    </div>

    <!-- Navigation buttons -->
    <div class="form-actions">
      <button 
        nz-button 
        nzType="default" 
        (click)="prevStep()" 
        [disabled]="currentStep() === 0">
        <nz-icon nzType="left"></nz-icon>
        Précédent
      </button>
      
      <button 
        nz-button 
        nzType="primary" 
        (click)="nextStep()" 
        *ngIf="currentStep() < 4">
        Suivant
        <nz-icon nzType="right"></nz-icon>
      </button>
      
      <button 
        nz-button 
        nzType="primary" 
        (click)="onSubmit()" 
        *ngIf="currentStep() === 4">
        <nz-icon nzType="check"></nz-icon>
        Créer l'événement
      </button>
    </div>
  </nz-card>

  <!-- Drawer pour nouveau client -->
  <nz-drawer
    [nzVisible]="clientDrawerVisible()"
    nzPlacement="right"
    nzTitle="Nouveau client"
    [nzWidth]="480"
    (nzOnClose)="closeClientDrawer()">
    <ng-container *nzDrawerContent>
      <app-client-form (clientCreated)="onClientCreated($event)"></app-client-form>
    </ng-container>
  </nz-drawer>
</div>
