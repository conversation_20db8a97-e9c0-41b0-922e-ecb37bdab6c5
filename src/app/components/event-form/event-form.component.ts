import { Component, signal } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { NzStepsModule } from 'ng-zorro-antd/steps';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzCardModule } from 'ng-zorro-antd/card';
import { NzFormModule } from 'ng-zorro-antd/form';
import { NzInputModule } from 'ng-zorro-antd/input';
import { NzSelectModule } from 'ng-zorro-antd/select';
import { NzRadioModule } from 'ng-zorro-antd/radio';
import { NzDatePickerModule } from 'ng-zorro-antd/date-picker';
import { NzInputNumberModule } from 'ng-zorro-antd/input-number';
import { NzSwitchModule } from 'ng-zorro-antd/switch';
import { NzDrawerModule } from 'ng-zorro-antd/drawer';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzDividerModule } from 'ng-zorro-antd/divider';
import { NzCheckboxModule } from 'ng-zorro-antd/checkbox';
import { ClientFormComponent } from '../client-form/client-form.component';

export interface EventType {
  value: string;
  label: string;
  category: 'particulier' | 'entreprise';
}

export interface Client {
  id?: string;
  nom: string;
  prenom: string;
  cin: string;
  ville: string;
  adresse: string;
  telephone1: string;
  telephone2?: string;
  email: string;
}

@Component({
  selector: 'app-event-form',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    NzStepsModule,
    NzButtonModule,
    NzCardModule,
    NzFormModule,
    NzInputModule,
    NzSelectModule,
    NzRadioModule,
    NzDatePickerModule,
    NzInputNumberModule,
    NzSwitchModule,
    NzDrawerModule,
    NzIconModule,
    NzDividerModule,
    NzCheckboxModule,
    ClientFormComponent
  ],
  templateUrl: './event-form.component.html',
  styleUrl: './event-form.component.css'
})
export class EventFormComponent {
  currentStep = signal(0);
  clientDrawerVisible = signal(false);

  // Formulaires pour chaque étape
  step1Form!: FormGroup;
  step2Form!: FormGroup;
  step3Form!: FormGroup;
  step4Form!: FormGroup;
  step5Form!: FormGroup;

  // Types d'événements
  eventTypes: EventType[] = [
    // Particulier
    { value: 'mariage', label: 'Mariage', category: 'particulier' },
    { value: 'fiancailles', label: 'Fiançailles', category: 'particulier' },
    { value: 'aqiqah', label: 'Aqiqah', category: 'particulier' },
    { value: 'repas-livre-part', label: 'Repas livré', category: 'particulier' },
    { value: 'soutenance', label: 'Soutenance', category: 'particulier' },
    { value: 'funerailles', label: 'Funérailles', category: 'particulier' },
    { value: 'anniversaire', label: 'Anniversaire', category: 'particulier' },
    { value: 'buffet-part', label: 'Buffet', category: 'particulier' },

    // Entreprise
    { value: 'cinema', label: 'Cinéma', category: 'entreprise' },
    { value: 'incentive', label: 'Incentive', category: 'entreprise' },
    { value: 'repas-livre-ent', label: 'Repas livré', category: 'entreprise' },
    { value: 'buffet-ent', label: 'Buffet', category: 'entreprise' },
    { value: 'seminaire', label: 'Séminaire', category: 'entreprise' },
    { value: 'fete-travail', label: 'Fête de travail', category: 'entreprise' },
    { value: 'restauration-collective', label: 'Restauration collective', category: 'entreprise' }
  ];

  // Clients existants (à remplacer par un service)
  existingClients: Client[] = [
    {
      id: '1',
      nom: 'Alami',
      prenom: 'Ahmed',
      cin: 'AB123456',
      ville: 'Casablanca',
      adresse: '123 Rue Mohammed V',
      telephone1: '0612345678',
      telephone2: '0522123456',
      email: '<EMAIL>'
    }
  ];

  // Options pour les packs
  packOptions = {
    mariage: ['bouffe', 'salle', 'negaffa', 'amaria', 'maida'],
    feteTravail: ['bouffe', 'salle']
  };

  constructor(private fb: FormBuilder) {
    this.initializeForms();
  }

  private initializeForms(): void {
    // Étape 1: Type d'événement
    this.step1Form = this.fb.group({
      category: ['', Validators.required],
      eventType: ['', Validators.required]
    });

    // Étape 2: Client
    this.step2Form = this.fb.group({
      clientType: ['existing', Validators.required],
      selectedClient: [''],
      newClient: this.fb.group({
        nom: ['', [Validators.required, Validators.minLength(2)]],
        prenom: ['', [Validators.required, Validators.minLength(2)]],
        cin: ['', [Validators.required, Validators.pattern(/^[A-Z]{1,2}[0-9]{6}$/)]],
        ville: ['', [Validators.required, Validators.minLength(2)]],
        adresse: ['', [Validators.required, Validators.minLength(5)]],
        telephone1: ['', [Validators.required, Validators.pattern(/^[0-9]{10}$/)]],
        telephone2: ['', [Validators.pattern(/^[0-9]{10}$/)]],
        email: ['', [Validators.required, Validators.email]]
      })
    });

    // Étape 3: Détails événement
    this.step3Form = this.fb.group({
      nbPersonnes: [null, [Validators.required, Validators.min(1)]],
      date: [null, Validators.required],
      ville: ['', Validators.required],
      adresse: ['', Validators.required],
      // Champs spéciaux pour mariage
      packs: [[]],
      photographe: [false],
      photographeCelebre: [false],
      dj: [false],
      issawa: [false],
      deqaMarrakchia: [false],
      ftour: [false],
      pieceMontee: [false],
      decoration: [false],
      decorationType: [''],
      typeMateriel: ['standard'],
      theme: ['']
    });

    // Étape 4: Paiement
    this.step4Form = this.fb.group({
      statut: ['non-paye', Validators.required],
      montantTotal: [null, [Validators.required, Validators.min(0)]],
      montantPaye: [0, [Validators.min(0)]],
      methodePaiement: [''],
      dateEcheance: [null],
      commentairePaiement: ['']
    });

    // Étape 5: Source et commentaires
    this.step5Form = this.fb.group({
      sourceTraffic: ['', Validators.required],
      commentaires: ['']
    });
  }

  get filteredEventTypes(): EventType[] {
    const category = this.step1Form.get('category')?.value;
    return category ? this.eventTypes.filter(type => type.category === category) : [];
  }

  get selectedEventType(): string {
    return this.step1Form.get('eventType')?.value;
  }

  get isClientTypeNew(): boolean {
    return this.step2Form.get('clientType')?.value === 'new';
  }

  nextStep(): void {
    if (this.validateCurrentStep()) {
      this.currentStep.update(step => Math.min(step + 1, 4));
    }
  }

  prevStep(): void {
    this.currentStep.update(step => Math.max(step - 1, 0));
  }

  private validateCurrentStep(): boolean {
    const currentStepIndex = this.currentStep();
    const forms = [this.step1Form, this.step2Form, this.step3Form, this.step4Form, this.step5Form];

    if (currentStepIndex < forms.length) {
      const currentForm = forms[currentStepIndex];

      if (currentStepIndex === 1 && this.isClientTypeNew) {
        // Valider le formulaire client si nouveau client
        const newClientForm = this.step2Form.get('newClient') as FormGroup;
        this.markFormGroupTouched(newClientForm);
        return newClientForm.valid;
      }

      this.markFormGroupTouched(currentForm);
      return currentForm.valid;
    }

    return true;
  }

  private markFormGroupTouched(formGroup: FormGroup): void {
    Object.keys(formGroup.controls).forEach(key => {
      const control = formGroup.get(key);
      control?.markAsTouched();
      if (control instanceof FormGroup) {
        this.markFormGroupTouched(control);
      }
    });
  }

  openClientDrawer(): void {
    this.clientDrawerVisible.set(true);
  }

  closeClientDrawer(): void {
    this.clientDrawerVisible.set(false);
  }

  onClientCreated(client: Client): void {
    this.existingClients.push(client);
    this.step2Form.patchValue({
      clientType: 'existing',
      selectedClient: client.id
    });
    this.closeClientDrawer();
  }

  onCategoryChange(): void {
    this.step1Form.patchValue({ eventType: '' });
  }

  onSubmit(): void {
    if (this.validateAllForms()) {
      const eventData = {
        ...this.step1Form.value,
        ...this.step2Form.value,
        ...this.step3Form.value,
        ...this.step4Form.value,
        ...this.step5Form.value
      };

      console.log('Données de l\'événement:', eventData);
      // Ici, vous pouvez envoyer les données à votre service
    }
  }

  private validateAllForms(): boolean {
    const forms = [this.step1Form, this.step2Form, this.step3Form, this.step4Form, this.step5Form];
    return forms.every(form => {
      this.markFormGroupTouched(form);
      return form.valid;
    });
  }
}
