.event-form-container {
  max-width: 1000px;
  margin: 0 auto;
  padding: 24px 32px 16px 32px; /* BEAUCOUP plus d'espace en haut et sur les côtés */
}

.form-card {
  box-shadow: var(--shadow-md);
  border-radius: var(--radius-lg);
  background: var(--apple-white);
  border: 1px solid var(--apple-gray-200);
}

/* Steps navigation - Apple style */
.steps-nav {
  margin-bottom: 24px;
  padding: 16px 24px;
  background: var(--apple-white);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--apple-gray-200);
}

.step-content {
  min-height: 400px;
  padding: 24px 0;
}

.step-content h3 {
  color: #8A3A45;
  margin-bottom: 24px;
  font-size: 20px;
  font-weight: 600;
}

.step-content h4 {
  color: #8A3A45;
  margin-bottom: 16px;
  font-size: 16px;
  font-weight: 500;
}

.step-content h5 {
  color: #8A3A45;
  margin-bottom: 8px;
  font-size: 14px;
  font-weight: 500;
}

/* Étape 1 - Choix de catégorie */
.category-choice {
  width: 100%;
}

.category-choice .ant-radio-group {
  width: 100%;
  display: block;
}

.category-cards {
  display: flex;
  gap: 24px;
  justify-content: center;
  flex-wrap: wrap;
  width: 100%;
  margin: 0 auto;
}

.category-card {
  flex: 1;
  max-width: 350px;
  min-width: 280px;
  border: 2px solid #f0f0f0;
  border-radius: 12px;
  padding: 32px 24px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  background: white;
  position: relative;
  overflow: visible;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 160px;
}

.category-card:hover {
  border-color: #8A3A45;
  box-shadow: 0 4px 16px rgba(138, 58, 69, 0.1);
  transform: translateY(-2px);
}

/* Styles pour les cartes sélectionnées */
.category-card.selected {
  border-color: #8A3A45;
  background: linear-gradient(135deg, #8A3A45 0%, #A04A56 100%);
  color: white;
  box-shadow: 0 6px 20px rgba(138, 58, 69, 0.3);
  transform: translateY(-2px);
}

.category-card.selected .card-content h4,
.category-card.selected .card-content p {
  color: white;
}

.category-card.selected .category-icon {
  color: white;
}

.card-content {
  position: relative;
  z-index: 2;
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.category-icon {
  font-size: 48px;
  margin-bottom: 16px;
  color: #8A3A45;
  transition: all 0.3s ease;
}

.category-card.ant-radio-button-wrapper-checked .category-icon {
  color: white;
}

.card-content h4 {
  margin: 0 0 8px 0;
  font-size: 18px;
  font-weight: 600;
  color: #333;
  transition: color 0.3s ease;
}

.card-content p {
  margin: 0;
  font-size: 14px;
  color: #666;
  line-height: 1.4;
  transition: color 0.3s ease;
}

/* Sections */
.section {
  margin-bottom: 32px;
  padding: 24px;
  background: #fafafa;
  border-radius: 8px;
  border-left: 4px solid #8A3A45;
}

.special-section {
  background: linear-gradient(135deg, #fafafa 0%, #f5f5f5 100%);
  border-left: 4px solid #8A3A45;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 24px;
}

.special-section h4 {
  color: #8A3A45;
  margin-bottom: 16px;
  font-weight: 600;
}

/* Styles pour les sous-sections organisées */
.options-section {
  background: #ffffff;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 16px;
}

.options-section h5 {
  color: #8A3A45;
  margin-bottom: 12px;
  font-weight: 600;
  font-size: 14px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.conditional-section {
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 6px;
  padding: 12px 16px;
  margin: 8px 0;
  border-left: 4px solid #8A3A45;
}

/* Bouton nouveau client */
.new-client-btn {
  width: 100%;
  height: 48px;
  border: 2px dashed #d9d9d9;
  border-radius: 8px;
  color: #8A3A45;
  font-weight: 500;
  transition: all 0.3s ease;
}

.new-client-btn:hover {
  border-color: #8A3A45;
  background: rgba(138, 58, 69, 0.05);
}

/* Résumé financier */
.financial-summary {
  margin-top: 24px;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border: 1px solid #dee2e6;
}

.summary-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #e9ecef;
}

.summary-row:last-child {
  border-bottom: none;
}

.paid-amount {
  color: #52c41a;
}

.remaining-amount {
  color: #fa8c16;
}

/* Récapitulatif événement */
.event-summary {
  margin-top: 24px;
  background: linear-gradient(135deg, #fff7e6 0%, #fff1d6 100%);
  border: 1px solid #ffd591;
}

.summary-section {
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid #ffd591;
}

.summary-section:last-child {
  margin-bottom: 0;
  border-bottom: none;
}

.summary-section h5 {
  margin-bottom: 4px;
  color: #8A3A45;
  font-weight: 600;
}

.summary-section p {
  margin: 0;
  color: #666;
  font-size: 14px;
}

/* Actions du formulaire */
.form-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 32px;
  padding-top: 24px;
  border-top: 1px solid #f0f0f0;
}

.form-actions button {
  min-width: 120px;
  height: 40px;
  border-radius: 8px;
  font-weight: 500;
}

/* Responsive */
@media (max-width: 768px) {
  .event-form-container {
    padding: 16px;
  }
  
  .category-cards {
    flex-direction: column;
    align-items: center;
  }
  
  .category-card {
    max-width: 100%;
    min-width: auto;
  }
  
  .form-actions {
    flex-direction: column;
    gap: 12px;
  }
  
  .form-actions button {
    width: 100%;
  }
}

/* Grilles de formulaire personnalisées */
.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
  margin-bottom: 16px;
}

.form-row-3 {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 16px;
  margin-bottom: 16px;
}

.form-col {
  width: 100%;
}

@media (max-width: 768px) {
  .form-row,
  .form-row-3 {
    grid-template-columns: 1fr;
    gap: 12px;
  }
}

/* Améliorations des formulaires */
.ant-form-item {
  margin-bottom: 16px;
}

.ant-form-item-label > label {
  color: #333;
  font-weight: 500;
}

.ant-input,
.ant-select-selector,
.ant-picker,
.ant-select,
.ant-input-number,
.ant-input-group {
  border-radius: 8px;
  border-color: #d9d9d9;
  transition: all 0.3s ease;
  width: 100% !important;
}

.ant-input:hover,
.ant-select-selector:hover,
.ant-picker:hover {
  border-color: #8A3A45;
}

.ant-input:focus,
.ant-select-focused .ant-select-selector,
.ant-picker-focused {
  border-color: #8A3A45;
  box-shadow: 0 0 0 2px rgba(138, 58, 69, 0.1);
}

/* Checkboxes et radios */
.ant-checkbox-wrapper,
.ant-radio-wrapper {
  font-weight: 500;
  color: #333;
}

.ant-checkbox-checked .ant-checkbox-inner,
.ant-radio-checked .ant-radio-inner {
  background-color: #8A3A45;
  border-color: #8A3A45;
}

.ant-checkbox:hover .ant-checkbox-inner,
.ant-radio:hover .ant-radio-inner {
  border-color: #8A3A45;
}

/* Steps personnalisés */
.ant-steps-item-finish .ant-steps-item-icon {
  background-color: #8A3A45;
  border-color: #8A3A45;
}

.ant-steps-item-process .ant-steps-item-icon {
  background-color: #8A3A45;
  border-color: #8A3A45;
}

.ant-steps-item-finish .ant-steps-item-title {
  color: #8A3A45;
}

/* Drawer styles */
.ant-drawer-content {
  background: #fafafa;
}

/* Cards améliorées */
.ant-card {
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.ant-card-head {
  border-bottom: 1px solid #f0f0f0;
  background: linear-gradient(135deg, #fafafa 0%, #f5f5f5 100%);
}

.ant-card-head-title {
  color: #8A3A45;
  font-weight: 600;
}
