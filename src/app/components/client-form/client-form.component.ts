import { Component, EventEmitter, Output } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { NzFormModule } from 'ng-zorro-antd/form';
import { NzInputModule } from 'ng-zorro-antd/input';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzMessageService } from 'ng-zorro-antd/message';

export interface Client {
  id?: string;
  nom: string;
  prenom: string;
  cin: string;
  ville: string;
  adresse: string;
  telephone1: string;
  telephone2?: string;
  email: string;
}

@Component({
  selector: 'app-client-form',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    NzFormModule,
    NzInputModule,
    NzButtonModule,
    NzIconModule
  ],
  templateUrl: './client-form.component.html',
  styleUrl: './client-form.component.css'
})
export class ClientFormComponent {
  @Output() clientCreated = new EventEmitter<Client>();

  clientForm!: FormGroup;
  isSubmitting = false;

  constructor(
    private fb: FormBuilder,
    private message: NzMessageService
  ) {
    this.initializeForm();
  }

  private initializeForm(): void {
    this.clientForm = this.fb.group({
      nom: ['', [Validators.required, Validators.minLength(2)]],
      prenom: ['', [Validators.required, Validators.minLength(2)]],
      cin: ['', [Validators.required, Validators.pattern(/^[A-Z]{1,2}[0-9]{6}$/)]],
      ville: ['', [Validators.required, Validators.minLength(2)]],
      adresse: ['', [Validators.required, Validators.minLength(5)]],
      telephone1: ['', [Validators.required, Validators.pattern(/^[0-9]{10}$/)]],
      telephone2: ['', [Validators.pattern(/^[0-9]{10}$/)]],
      email: ['', [Validators.required, Validators.email]]
    });
  }

  onSubmit(): void {
    if (this.clientForm.valid) {
      this.isSubmitting = true;
      
      // Simulation d'un appel API
      setTimeout(() => {
        const newClient: Client = {
          id: this.generateId(),
          ...this.clientForm.value
        };
        
        this.clientCreated.emit(newClient);
        this.message.success('Client créé avec succès !');
        this.resetForm();
        this.isSubmitting = false;
      }, 1000);
    } else {
      this.markFormGroupTouched();
      this.message.error('Veuillez corriger les erreurs dans le formulaire');
    }
  }

  private generateId(): string {
    return 'client_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
  }

  private markFormGroupTouched(): void {
    Object.keys(this.clientForm.controls).forEach(key => {
      const control = this.clientForm.get(key);
      control?.markAsTouched();
    });
  }

  private resetForm(): void {
    this.clientForm.reset();
    Object.keys(this.clientForm.controls).forEach(key => {
      const control = this.clientForm.get(key);
      control?.markAsUntouched();
      control?.markAsPristine();
    });
  }

  // Getters pour les validations
  get nomError(): string | null {
    const control = this.clientForm.get('nom');
    if (control?.touched && control?.errors) {
      if (control.errors['required']) return 'Le nom est requis';
      if (control.errors['minlength']) return 'Le nom doit contenir au moins 2 caractères';
    }
    return null;
  }

  get prenomError(): string | null {
    const control = this.clientForm.get('prenom');
    if (control?.touched && control?.errors) {
      if (control.errors['required']) return 'Le prénom est requis';
      if (control.errors['minlength']) return 'Le prénom doit contenir au moins 2 caractères';
    }
    return null;
  }

  get cinError(): string | null {
    const control = this.clientForm.get('cin');
    if (control?.touched && control?.errors) {
      if (control.errors['required']) return 'Le CIN est requis';
      if (control.errors['pattern']) return 'Format CIN invalide (ex: AB123456)';
    }
    return null;
  }

  get villeError(): string | null {
    const control = this.clientForm.get('ville');
    if (control?.touched && control?.errors) {
      if (control.errors['required']) return 'La ville est requise';
      if (control.errors['minlength']) return 'La ville doit contenir au moins 2 caractères';
    }
    return null;
  }

  get adresseError(): string | null {
    const control = this.clientForm.get('adresse');
    if (control?.touched && control?.errors) {
      if (control.errors['required']) return 'L\'adresse est requise';
      if (control.errors['minlength']) return 'L\'adresse doit contenir au moins 5 caractères';
    }
    return null;
  }

  get telephone1Error(): string | null {
    const control = this.clientForm.get('telephone1');
    if (control?.touched && control?.errors) {
      if (control.errors['required']) return 'Le téléphone principal est requis';
      if (control.errors['pattern']) return 'Format téléphone invalide (10 chiffres)';
    }
    return null;
  }

  get telephone2Error(): string | null {
    const control = this.clientForm.get('telephone2');
    if (control?.touched && control?.errors) {
      if (control.errors['pattern']) return 'Format téléphone invalide (10 chiffres)';
    }
    return null;
  }

  get emailError(): string | null {
    const control = this.clientForm.get('email');
    if (control?.touched && control?.errors) {
      if (control.errors['required']) return 'L\'email est requis';
      if (control.errors['email']) return 'Format email invalide';
    }
    return null;
  }
}
