<nz-layout class="app-layout">
  <nz-sider class="menu-sidebar"
    nzCollapsible
    nzWidth="256px"
    nzBreakpoint="md"
    [(nzCollapsed)]="isCollapsed"
    [nzTrigger]="null"
  >
    <div class="sidebar-logo">
      <a href="/" target="_self">
        <img [src]="isCollapsed ? 'assets/images/logo-min.png' : 'assets/images/logo.png'" alt="Traiteria Logo">
      </a>
    </div>
    <ul nz-menu nzTheme="dark" nzMode="inline" [nzInlineCollapsed]="isCollapsed">
      <li nz-submenu nzOpen nzTitle="Dashboard" nzIcon="dashboard">
        <ul>
          <li nz-menu-item routerLinkActive="ant-menu-item-selected" [routerLinkActiveOptions]="{exact: true}">
            <a routerLink="/welcome">Accueil</a>
          </li>
          <li nz-menu-item>
            <a>Statistiques</a>
          </li>
          <li nz-menu-item>
            <a>Rapports</a>
          </li>
        </ul>
      </li>
      <li nz-submenu nzOpen nzTitle="Événements" nzIcon="calendar">
        <ul>
          <li nz-menu-item [class.ant-menu-item-selected]="isEventsListActive()">
            <a routerLink="/events">Liste des événements</a>
          </li>
          <li nz-menu-item routerLinkActive="ant-menu-item-selected" [routerLinkActiveOptions]="{exact: true}">
            <a routerLink="/events/new">Nouvel événement</a>
          </li>
        </ul>
      </li>
      <li nz-submenu nzOpen nzTitle="Clients" nzIcon="user">
        <ul>
          <li nz-menu-item>
            <a>Liste des clients</a>
          </li>
          <li nz-menu-item>
            <a>Nouveau client</a>
          </li>
        </ul>
      </li>
    </ul>
  </nz-sider>
  <nz-layout class="main-content" [class.collapsed]="isCollapsed">
    <nz-header>
      <div class="app-header">
        <span class="header-trigger" (click)="isCollapsed = !isCollapsed">
          <nz-icon class="trigger" [nzType]="isCollapsed ? 'menu-unfold' : 'menu-fold'" />
        </span>
      </div>
    </nz-header>
    <nz-content>
      <div class="inner-content">
        <router-outlet></router-outlet>
      </div>
    </nz-content>
  </nz-layout>
</nz-layout>
